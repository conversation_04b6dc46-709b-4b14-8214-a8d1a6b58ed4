-- 测点管理-评价指标
alter table dam_point_evaluate add abnormal_up varchar(64) null comment '异常最大值';
alter table dam_point_evaluate add abnormal_down varchar(64) null comment '异常最小值';


-- 测点管理-测点报警信息
alter table dam_point_alarm add alarm_type tinyint default 1 null comment '报警类型：1-异常报警；2-错误报警';

-- 测点数据
alter table dam_point_data_json add review_status tinyint default 1 null comment '审核状态（0：未审核；1：审核通过；2：审核不通过）';
alter table dam_point_data_json add reviewer varchar(64) null comment '审核人';
alter table dam_point_data_json add review_name varchar(64) null comment '审核人名称';
alter table dam_point_data_json add review_opinion varchar(256) null comment '审核意见';



-- 历史数据处理
-- 测点数据，正常数据：审核状态为1，审核人为“系统”；异常数据：审核状态为 0
update dam_point_data_json set review_status=1,review_name='系统',reviewer='-1'  where data_status=1;
update dam_point_data_json set review_status=0  where data_status=0;

-- 测点计算参数
ALTER TABLE dam_instrument_param ADD COLUMN thing_weight int DEFAULT NULL COMMENT '权重，数字越小越靠前' AFTER decimal_limit;

--仪器台账
create table dam_instrument_account
(
    id                      bigint auto_increment comment '主键id'
        primary key,
    project_id              bigint                             null comment '项目id',
    production_number       varchar(255)                       null comment '生产编号',
    instrument_model        varchar(255)                       null comment '仪器型号',
    instrument_id           bigint                             null comment '仪器类型id',
    instrument_manufacturer varchar(255)                       null comment '生产厂商',
    remarks                 varchar(255)                       null comment '备注',
    creator                 varchar(64)                        null comment '创建者',
    create_time             datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updater                 varchar(64)                        null comment '更新者',
    update_time             datetime default CURRENT_TIMESTAMP null comment '更新时间',
    deleted                 bit      default b'0'              null comment '是否删除，0未删除，1删除，默认为0'
)
    comment '仪器台账';

-- 索引
create index index_point_time_data_type_status on dam_point_data_json(point_id,deleted,point_time,data_type,data_status,review_status);

--测点信息
ALTER TABLE dam_point DROP COLUMN produce_name;
ALTER TABLE dam_point DROP COLUMN produce_code;
ALTER TABLE dam_point DROP COLUMN produce_model;
ALTER TABLE dam_point ADD COLUMN account_id bigint DEFAULT NULL COMMENT '仪器台账id' AFTER instrument_id;

-- 测点数据导入
ALTER TABLE dam_point_data_import ADD COLUMN import_status int DEFAULT 1 COMMENT '导入状态：1-正在导入，2-导入成功，3-导入失败' AFTER file_path;
ALTER TABLE dam_point_data_import ADD COLUMN message varchar(512) DEFAULT NULL COMMENT '导入信息' AFTER import_status;
-- 测点数据导入：历史数据变更
update dam_point_data_import set import_status=2 where import_status =1;
