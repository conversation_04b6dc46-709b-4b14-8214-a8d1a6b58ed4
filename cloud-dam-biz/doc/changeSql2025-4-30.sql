-- 测点信息管理
alter table dam_point add column automated_frequency varchar(64) default null comment '自动化观测频次' after point_stage;
alter table dam_point add column manual_frequency varchar(64) default null comment '人工观测频次' after automated_frequency;

-- 仪器类型
alter table dam_instrument add column template_id bigint default null comment '仪器类型模板id' after project_id;

-- 测点评价指标
alter table dam_point_evaluate add column evaluate_extreme_id bigint default null comment '测点评价极值id' after instrument_model_id;

-- 仪器类型模板
create table dam_instrument_template
(
    id                bigint                             not null comment '主键id'
        primary key,
    instrument_name   varchar(64)                        null comment '仪器类型模板名称',
    measure_principle tinyint                            null comment '测量原理，1：差阻式，2：振弦式，3：电容式，4：电感式，5：其它',
    measure_item      tinyint                            null comment '监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量',
    icon_url          varchar(500)                       null comment '图标地址',
    remark            varchar(255)                       null comment '备注',
    creator           varchar(64)                        null comment '创建者',
    create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updater           varchar(64)                        null comment '更新者',
    update_time       datetime default CURRENT_TIMESTAMP null comment '更新时间',
    deleted           bit      default b'0'              null comment '是否删除，0未删除，1删除，默认为0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin comment '仪器类型模板';


create table dam_instrument_model_template
(
    id                   bigint auto_increment comment '主键id'
        primary key,
    instrument_id        bigint                             null comment '仪器类型模板id',
    thing_name           varchar(64)                        null comment '分量名称',
    thing_identity       varchar(64)                        null comment '分量标识符',
    thing_unit           varchar(32)                        null comment '单位',
    data_type            tinyint                            null comment '数据类型，1：整数型，2：浮点型，3：双精度',
    down_limit           varchar(64)                        null comment '下限',
    up_limit             varchar(64)                        null comment '上限',
    decimal_limit        int                                null comment '小数位',
    thing_type           tinyint                            null comment '分量类型，1：原始值，2：中间值，3：成果值',
    thing_weight         int                                null comment '权重，数字越小越靠前',
    creator              varchar(64)                        null comment '创建者',
    create_time          datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updater              varchar(64)                        null comment '更新者',
    update_time          datetime default CURRENT_TIMESTAMP null comment '更新时间',
    deleted              bit      default b'0'              null comment '是否删除，0未删除，1删除，默认为0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin comment '仪器类型模板-测量分量';

create table dam_instrument_param_template
(
    id             bigint auto_increment comment '主键id'
        primary key,
    instrument_id  bigint                             null comment '仪器类型模板id',
    thing_name     varchar(64)                        null comment '参数名称',
    thing_identity varchar(64)                        null comment '参数标识符',
    thing_unit     varchar(32)                        null comment '单位',
    decimal_limit  int                                null comment '小数位',
    thing_weight   int                                null comment '权重，数字越小越靠前',
    creator        varchar(64)                        null comment '创建者',
    create_time    datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updater        varchar(64)                        null comment '更新者',
    update_time    datetime default CURRENT_TIMESTAMP null comment '更新时间',
    deleted        bit      default b'0'              null comment '是否删除，0未删除，1删除，默认为0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin comment '仪器类型模板-计算参数';

create table dam_point_evaluate_extreme
(
    id                   bigint auto_increment comment '主键id'
        primary key,
    project_id           bigint                             not null comment '项目id',
    point_id             bigint                             not null comment '测点id',
    instrument_model_id  bigint                             not null comment '分量id',
    start_time           datetime                           null comment '开始时间',
    end_time             datetime                           null comment '结束时间（小于等于当前时间）',
    data_type            tinyint                            null comment '采集类型(0：全部，1：自动化采集，2：人工录入）',
    extreme_type         tinyint                            null comment '最值法(1-极值百分比浮动，2-极值固定值浮动，3-固定数值)',
    abnormal_down_type   tinyint                            null comment '异常最小值(1-极小值，2-极大值)',
    abnormal_down_symbol tinyint                            null comment '异常最小值(1-加，2-减)',
    abnormal_down        varchar(64)                        null comment '异常最小值',
    waring_down_type     tinyint                            null comment '正常最小值(1-极小值，2-极大值)',
    waring_down_symbol   tinyint                            null comment '正常最小值(1-加，2-减)',
    waring_down          varchar(64)                        null comment '正常最小值',
    abnormal_up_type     tinyint                            null comment '异常最大值(1-极小值，2-极大值)',
    abnormal_up_symbol   tinyint                            null comment '异常最大值(1-加，2-减)',
    abnormal_up          varchar(64)                        null comment '异常最大值',
    waring_up_type       tinyint                            null comment '正常最大值(1-极小值，2-极大值)',
    waring_up_symbol     tinyint                            null comment '正常最大值(1-加，2-减)',
    waring_up            varchar(64)                        null comment '正常最大值',
    creator              varchar(64)                        null comment '创建者',
    create_time          datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updater              varchar(64)                        null comment '更新者',
    update_time          datetime default CURRENT_TIMESTAMP null comment '更新时间',
    deleted              bit      default b'0'              null comment '是否删除，0未删除，1删除，默认为0'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin comment '测点评价指标极值';

-- 查看索引是否正确
-- show index from dam_point_data;
-- create index dam_point_data_point_id_IDX on dam_point_data(point_id,instrument_model_id,point_time,deleted,data_type,thing_value);

