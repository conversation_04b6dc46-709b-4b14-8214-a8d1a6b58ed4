-- `test-cloud-dam`.dam_index_middle_bottom definition
CREATE TABLE `dam_index_middle_bottom`
(
    id                bigint auto_increment comment '主键'
        primary key,
    project_id        bigint                             not null comment '项目id',
    instrument_name   varchar(100)                       null comment '仪器类型名称',
    instrument_count  bigint   default 0                 null comment '仪器数量',
    instrument_rate   varchar(100)                       null comment '仪器占比%',
    point_data_count  bigint   default 0                 null comment '观测记录数量',
    point_time_recent datetime                           null comment '最近观测时间',
    point_time_first  datetime                           null comment '最早观测时间',
    creator           varchar(64)                        null comment '创建者',
    create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updater           varchar(64)                        null comment '更新者',
    update_time       datetime default CURRENT_TIMESTAMP null comment '更新时间',
    deleted           bit      default b'0'              null comment '是否删除，0未删除，1删除，默认为0',
    generate_time     varchar(100)                       null comment '生成时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='首页中间和底部数据';

-- `test-cloud-dam`.dam_index_head definition
CREATE TABLE `dam_index_head`
(
    id                   bigint auto_increment comment '主键'
        primary key,
    project_id           bigint                             not null comment '项目id',
    point_device_total   bigint   default 0                 null comment '监测仪器数量',
    point_device_today   bigint   default 0                 null comment '监测仪器今日新增数量',
    point_data_total     bigint   default 0                 null comment '监测数据数量',
    point_data_today     bigint   default 0                 null comment '监测数据今日新增数量',
    device_mcu_total     bigint   default 0                 null comment '自动化设备数量',
    device_mcu_today     bigint   default 0                 null comment '自动化设备今日新增数量',
    device_mcu_run_total bigint   default 0                 null comment '在测自动化仪器数量',
    device_mcu_run_today bigint   default 0                 null comment '在测自动化仪器今日新增数量',
    point_data_mcu_total bigint   default 0                 null comment '自动化监测数据数量',
    point_data_mcu_today bigint   default 0                 null comment '自动化监测数据今日新增数量',
    creator              varchar(64)                        null comment '创建者',
    create_time          datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updater              varchar(64)                        null comment '更新者',
    update_time          datetime default CURRENT_TIMESTAMP null comment '更新时间',
    deleted              bit      default b'0'              null comment '是否删除，0未删除，1删除，默认为0',
    generate_time        varchar(100)                       null comment '生成时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='大坝首页头部信息';