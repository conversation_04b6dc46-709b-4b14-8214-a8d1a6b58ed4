<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.powerchina.bjy</groupId>
        <artifactId>cloud-link-dam</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cloud-dam-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        大坝 模块：通用业务，支撑上层业务。
    </description>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <!-- 依赖服务 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-env</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-system-api</artifactId>
            <version>${revision.system}</version>
        </dependency>
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-infra-api</artifactId>
            <version>${revision.infra}</version>
        </dependency>

        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-dam-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-iot-api</artifactId>
            <version>${revision.iot}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-biz-data-permission</artifactId>
            <version>${revision.framework}</version>
        </dependency>
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-biz-tenant</artifactId>
            <version>${revision.framework}</version>
        </dependency>
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-biz-ip</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-security</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-mybatis</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-redis</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-rpc</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-job</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-excel</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-monitor</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <dependency>
            <groupId>org.dromara.hutool</groupId>
            <artifactId>hutool-extra</artifactId> <!-- 邮件 -->
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-mqtt</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId> <!-- 文件客户端：文件类型的识别 -->
        </dependency>

        <!-- 计算引擎-->
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.2.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-v5-client-spring-boot</artifactId>
            <version>2.3.3</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.57</version>
        </dependency>

        <dependency>
            <groupId>com.taosdata.jdbc</groupId>
            <artifactId>taos-jdbcdriver</artifactId>
            <version>3.6.3</version>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--打包时候不要压缩模板文件，排除扩展名为下xlsx的文件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>