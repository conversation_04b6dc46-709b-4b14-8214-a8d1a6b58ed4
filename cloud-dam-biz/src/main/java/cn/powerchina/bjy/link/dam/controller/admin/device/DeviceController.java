package cn.powerchina.bjy.link.dam.controller.admin.device;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.config.DamConfig;
import cn.powerchina.bjy.link.dam.controller.admin.device.bo.DeviceBO;
import cn.powerchina.bjy.link.dam.controller.admin.device.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.enums.BindTypeEnum;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.api.devicegroup.DeviceGroupApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.error;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 大坝设备")
@RestController
@RequestMapping("/dam/device")
@Validated
public class DeviceController {

    @Resource
    private DeviceService deviceService;
    @Resource
    private DeviceGroupApi deviceGroupApi;


    @PutMapping("/update")
    @Operation(summary = "更新大坝设备")
//    @PreAuthorize("@ss.hasPermission('dam:device:update')")
    public CommonResult<Boolean> updateDevice(@Valid @RequestBody DeviceSaveReqVO updateReqVO) {
        deviceService.updateDevice(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得大坝设备")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:device:query')")
    public CommonResult<DeviceRespVO> getDevice(@RequestParam("id") Long id) {
        DeviceBO device = deviceService.getDeviceBO(id);
        return success(BeanUtils.toBean(device, DeviceRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得大坝设备分页")
//    @PreAuthorize("@ss.hasPermission('dam:device:query')")
    public CommonResult<PageResult<DeviceRespVO>> getDevicePage(@Valid DevicePageReqVO pageReqVO) {
        PageResult<DeviceBO> pageResult = deviceService.getDeviceBOPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeviceRespVO.class));
    }

    @PostMapping("/bind")
    @Operation(summary = "测点绑定与解绑")
    public CommonResult<Boolean> deviceBind(@Valid @RequestBody DeviceBindReqVO reqVO) {
        deviceService.deviceBindPoint(reqVO);
        return success(true);
    }

    @PostMapping("/collect")
    @Operation(summary = "设备实时采集")
    public CommonResult<Boolean> deviceDataCollect(@Valid @RequestBody DeviceCollectReqVO reqVO) {
        deviceService.deviceDataCollect(reqVO);
        return success(true);
    }

    @PostMapping("/clock_sync")
    @Operation(summary = "设备时钟同步")
    public CommonResult<Boolean> deviceClockSync(@Valid @RequestBody DeviceCollectReqVO reqVO) {
        deviceService.deviceClockSync(reqVO);
        return success(true);
    }

    @PostMapping("/cancelBindDevice")
    @Operation(summary = "取消绑定设备")
    @Parameter(name = "deviceId", description = "设备列表id", required = true)
    public CommonResult<?> cancelBindDevice(@RequestParam("deviceId") Long deviceId) {
        DeviceDO deviceDO = deviceService.getDevice(deviceId);
        if (Objects.isNull(deviceDO)) {
            return error(400, "不存在的设备不能被删除！");
        }
        if (Objects.equals(deviceDO.getBindType(), BindTypeEnum.YES.getType())) {
            return error(400, "已绑定测点设备不能被删除！");
        }

        deviceService.deleteGatewayDevice(deviceId);
        return success(true);
    }


    /**
     * 空间下的没有绑定的设备
     *
     * @return 设备信息
     */
    @PostMapping("/spaceDevices")
    @Operation(summary = "空间下的没有绑定的设备")
    public CommonResult<PageResult<DeviceIotRespVO>> spaceNoBindDevices(@RequestBody DeviceIotPageReqVO iotPageReqVO) {
        PageResult<DeviceIotRespVO> pageResult = deviceService.spaceNoBindDevices(iotPageReqVO);
        return success(pageResult);
    }

    @GetMapping("/detail")
    @Operation(summary = "详情")
    @Parameter(name = "deviceId", description = "设备id", required = true)
    public CommonResult<DeviceDetailResVO> detail(@RequestParam("deviceId") Long deviceId) {
        DeviceDetailResVO deviceDetailResVO = deviceService.getDeviceDetail(deviceId);
        return success(deviceDetailResVO);
    }


    @PostMapping("/secret")
    @Operation(summary = "加解密")
    @PermitAll // 添加此注解表示允许匿名访问
    public CommonResult<Boolean> secret(@RequestBody DeviceSecretVO secretVO) throws Exception {
        deviceService.secret(secretVO);
        return success(true);
    }

}