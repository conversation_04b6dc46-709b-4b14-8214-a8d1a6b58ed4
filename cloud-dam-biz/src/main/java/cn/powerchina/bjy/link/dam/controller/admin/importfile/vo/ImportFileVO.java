package cn.powerchina.bjy.link.dam.controller.admin.importfile.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Data
public class ImportFileVO {
    @Schema(description = "项目id", example = "8231")
    private Long projectId;

    @Schema(description = "节点id", example = "9170")
    private Long nodeId;
    @Schema(description = "指定导入开始时间")

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startTime;

    @Schema(description = "指定导入结束时间")

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endTime;

    @Schema(description = "导入新数据,0否，1是")
    private Boolean uploadNew;

    private List<ImportFileSaveReqVO> importFileSaveReqVOS;
}
