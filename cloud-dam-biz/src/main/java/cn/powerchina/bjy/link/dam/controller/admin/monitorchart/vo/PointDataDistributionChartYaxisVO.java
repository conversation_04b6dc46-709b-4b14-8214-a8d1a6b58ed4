package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 监测图形-分布图--y轴
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "y轴")
@Data
public class PointDataDistributionChartYaxisVO {

    @Schema(description = "类型")
    private String type;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "位置")
    private String position;

    @Schema(description = "是否对齐")
    private Boolean alignTicks;

    @Schema(description = "轴线")
    private PointDataDistributionChartYaxisLineVO axisLine;

    @Schema(description = "刻度")
    private PointDataDistributionChartAxisTickVO axisTick;

    @Schema(description = "splitLine")
    private PointDataDistributionChartYsplitLineVO splitLine;
}
