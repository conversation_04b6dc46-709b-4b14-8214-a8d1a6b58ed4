package cn.powerchina.bjy.link.dam.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.dam.service.pointdata.PointDataService;
import cn.powerchina.bjy.link.iot.model.DeviceClockSyncTransportModel;
import cn.powerchina.bjy.link.iot.model.DeviceDataTransportModel;
import cn.powerchina.bjy.link.iot.model.EdgeResultModel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import static cn.powerchina.bjy.link.iot.enums.IotTopicConstant.*;

/**
 * @Description: 接收设备上报的时钟同步数据
 * @Author: zhaoqiang
 * @CreateDate: 2024/10/9
 */
@Slf4j
@Component
@RocketMQMessageListener( topic = TOPIC_DEVICE_CLOCK_SYNC_DATA, consumerGroup = GROUP_DEVICE_CLOCK_SYNC_DATA)
public class DeviceClockSyncReceiver implements RocketMQListener {

    @Resource
    private DeviceService deviceService;


    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        try {
            // 解析消息体
            DeviceClockSyncTransportModel transportModel = parseMessageBody(messageView);
            if (transportModel == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS;
            }
            log.info("receiver message {}", JSONObject.toJSON(transportModel));
            DeviceClockSyncTransportModel.ClockSyncData clockSyncData = transportModel.getClockSyncData();
            String data = clockSyncData.getData();
            // TODO 从这里处理data
            deviceService.updateDeviceClockTime(transportModel.getDeviceCode(), null, transportModel.getCurrentTime());
        } catch (Exception e) {
            log.error("指令下发属性解析异常--->error,entityDTO={}", JsonUtils.toJsonString(messageView), e);
        }
        return ConsumeResult.SUCCESS;

    }
    /**
     * 解析消息体为实体类
     */
    private DeviceClockSyncTransportModel parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, DeviceClockSyncTransportModel.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
}
