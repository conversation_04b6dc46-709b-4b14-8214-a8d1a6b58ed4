package cn.powerchina.bjy.link.dam.controller.admin.importfile.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
@Accessors(chain = false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UploadFileVO { ;

    @Schema(description = "项目id", example = "8231")
    private Long projectId;

    @Schema(description = "节点id", example = "9170")
    private Long nodeId;

    @Schema(description = "excel文件数组")
    private List<MultipartFile> files;
}
