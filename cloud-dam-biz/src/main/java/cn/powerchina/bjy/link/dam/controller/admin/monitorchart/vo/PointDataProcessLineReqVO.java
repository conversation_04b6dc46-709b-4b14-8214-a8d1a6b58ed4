package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description: 监测图形-过程线的参数
 * @Author: yangjingtao
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-过程线 Request VO")
@Data
@ToString(callSuper = true)
public class PointDataProcessLineReqVO {

    @Schema(description = "项目id")
    @NotNull(message = "项目不能为空")
    private Long projectId;

    @ArraySchema(
            arraySchema = @Schema(description = "测点id列表"),
            schema = @Schema(implementation = Long.class)
    )
    @NotNull(message = "测点id不能为空")
    private List<Long> pointIdList;

    @ArraySchema(
            arraySchema = @Schema(description = "分量id"),
            schema = @Schema(implementation = Long.class)
    )
    @NotNull(message = "分量id不能为空")
    private List<Long> instrumentModelIdList;

    @Schema(description = "开始时间，yyyy-MM-dd HH:mm:ss格式")
    private String minTime;

    @Schema(description = "截至时间，yyyy-MM-dd HH:mm:ss格式")
    private String maxTime;

    @ArraySchema(
            arraySchema = @Schema(description = "采集类型(1：自动化采集，2：人工录入）"),
            schema = @Schema(implementation = Integer.class)
    )
    private List<Integer> dataTypeList;

    @ArraySchema(
            arraySchema = @Schema(description = "数据状态(0：未判定，1：正常数据，2：异常，3：错误数据）"),
            schema = @Schema(implementation = Integer.class)
    )
    private List<Integer> dataStatusList;

    @ArraySchema(
            arraySchema = @Schema(description = "审核状态（0：未审核；1：审核通过；2：审核不通过）"),
            schema = @Schema(implementation = Integer.class)
    )
    private List<Integer> reviewStatusList;
}
