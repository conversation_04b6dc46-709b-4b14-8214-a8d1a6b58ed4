package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 监测图形-过程线
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "过程线配置")
@Data
public class PointDataProcessLineOptionVO {

    @Schema(description = "背景颜色")
    private String backgroundColor;

    @Schema(description = "标题")
    private PointDataProcessLineTitleVO title;

    @Schema(description = "鼠标提示")
    private PointDataProcessLineTootipVO tootip;

    @Schema(description = "图例")
    private PointDataProcessLineLegendVO legend;

    @Schema(description = "x轴")
    @JsonProperty("xAxis")
    private PointDataProcessLineXaxisVO xAxis;

    @Schema(description = "y轴")
    @JsonProperty("yAxis")
    private List<PointDataProcessLineYaxisVO> yAxis;

    @Schema(description = "数据")
    private List<PointDataProcessLineSeriesVO> series;

    @Schema(description = "dataZoom")
    private List<PointDataProcessLineDataZoomVO> dataZoom;

    @Schema(description = "toolbox")
    private PointDataProcessLineTooboxVO toolbox;

    @Schema(description = "grid")
    private PointDataProcessLineGridVO grid;
}