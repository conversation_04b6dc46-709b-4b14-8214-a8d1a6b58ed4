package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Author: yangjingt<PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "splitLine")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartYsplitLineVO {

    @Schema(description = "show")
    private Boolean show;

    @Schema(description = "lineStyle")
    private PointDataDistributionChartYsplitLineStyleVO lineStyle;
}
