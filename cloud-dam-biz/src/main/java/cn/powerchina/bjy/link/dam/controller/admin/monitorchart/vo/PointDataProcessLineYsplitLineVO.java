package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description:
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "splitLine")
@Data
public class PointDataProcessLineYsplitLineVO {

    @Schema(description = "show")
    private Boolean show;

    @Schema(description = "lineStyle")
    private PointDataProcessLineYsplitLineStyleVO lineStyle;
}