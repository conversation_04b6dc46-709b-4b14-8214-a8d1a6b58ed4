package cn.powerchina.bjy.link.dam.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.dam.enums.BindTypeEnum;
import cn.powerchina.bjy.link.dam.service.authdevice.AuthDeviceService;
import cn.powerchina.bjy.link.dam.service.authproduct.AuthProductService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceRespDTO;
import cn.powerchina.bjy.link.iot.api.devicegroupdetail.DeviceGroupDetailApi;
import cn.powerchina.bjy.link.iot.enums.DeviceChangeEnum;
import cn.powerchina.bjy.link.iot.model.DeviceDataTransportModel;
import cn.powerchina.bjy.link.iot.model.DeviceGroupChangeModel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static cn.powerchina.bjy.link.iot.enums.IotTopicConstant.GROUP_DEVICE_GROUP_CHANGE;
import static cn.powerchina.bjy.link.iot.enums.IotTopicConstant.TOPIC_DEVICE_GROUP_CHANGE;

/**
 * @Description: 设备分组信息变更
 * @Author: zhaoqiang
 * @CreateDate: 2024/10/9
 */
@Slf4j
@Component
//@RocketMQMessageListener( topic = TOPIC_DEVICE_GROUP_CHANGE, consumerGroup = GROUP_DEVICE_GROUP_CHANGE)
public class DeviceGroupChangeReceiver implements RocketMQListener {

    @Autowired
    private AuthDeviceService authDeviceService;

    @Autowired
    private AuthProductService authProductService;

    @Resource
    private DeviceMapper deviceMapper;

    @Autowired
    private DeviceGroupDetailApi deviceGroupDetailApi;

    @Autowired
    private PointService pointService;



    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        try {
            // 解析消息体
            DeviceGroupChangeModel deviceGroupChangeModel = parseMessageBody(messageView);
            if (deviceGroupChangeModel == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS;
            }
            log.info("receiver message {}", JSONObject.toJSON(deviceGroupChangeModel));
            if (Objects.isNull(deviceGroupChangeModel.getDeviceGroupId())) {
                return ConsumeResult.SUCCESS;
            }
            if (CollectionUtils.isEmpty(deviceGroupChangeModel.getDeviceCodeList())) {
                return ConsumeResult.SUCCESS;
            }
            //根据deviceGroupId查询使用该分组的项目
            List<Long> projectList = authDeviceService.getProjectByDeviceGroupId(deviceGroupChangeModel.getDeviceGroupId());
            if (CollectionUtils.isEmpty(projectList)) {
                return ConsumeResult.SUCCESS;
            }
            projectList.forEach(projectId -> {
                //查询当前项目已经存在的设备
                List<DeviceDO> deviceDbList = Optional.ofNullable(deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getProjectId, projectId))).orElse(new ArrayList<>());
                //分组中新增设备
                if (DeviceChangeEnum.ADD.getType().equals(deviceGroupChangeModel.getChangeType())) {
                    //当前项目已经存在的设备编码
                    List<String> deviceDbCodeList = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(deviceDbList)) {
                        deviceDbCodeList.addAll(deviceDbList.stream().map(DeviceDO::getDeviceCode).toList());
                    }
                    //需要新增的设备列表
                    List<DeviceDO> deviceAddList = new ArrayList<>();
                    //设备对应的产品编码
                    Set<String> productCodeSet = new HashSet<>();
                    //分组新增的设备
                    CommonResult<List<DeviceRespDTO>> deviceByDeviceCodeList = deviceGroupDetailApi.getDeviceByDeviceCodeList(deviceGroupChangeModel.getDeviceCodeList());
                    for (DeviceRespDTO respDTO : deviceByDeviceCodeList.getData()) {
                        productCodeSet.add(respDTO.getProductCode());
                        DeviceDO deviceDO = new DeviceDO();
                        deviceDO.setProjectId(projectId);
                        deviceDO.setBindType(BindTypeEnum.NO.getType());
                        org.springframework.beans.BeanUtils.copyProperties(respDTO, deviceDO, "id");
                        //数据库里不包含就是新增的
                        if (!deviceDbCodeList.contains(deviceDO.getDeviceCode())) {
                            deviceAddList.add(deviceDO);
                        }
                    }
                    //插入新增的设备
                    deviceMapper.insertBatch(deviceAddList);
                    //将设备对应的产品添加到产品授权
                    authProductService.addAuthProductByProductCode(projectId, productCodeSet.stream().toList());
                }
                if (DeviceChangeEnum.DELETE.getType().equals(deviceGroupChangeModel.getChangeType())) {
                    if (!CollectionUtils.isEmpty(deviceDbList)) {
                        List<DeviceDO> deviceRemoveList = deviceDbList.stream().filter(item -> deviceGroupChangeModel.getDeviceCodeList().contains(item.getDeviceCode())).toList();
                        deviceMapper.deleteBatchIds(deviceRemoveList.stream().map(DeviceDO::getId).toList());
                        //移出的设备需要通知测点解绑
                        List<Long> pointIdList = deviceRemoveList.stream().filter(item -> Objects.equals(item.getBindType(), BindTypeEnum.YES.getType())).map(DeviceDO::getPointId).toList();
                        if (!CollectionUtils.isEmpty(pointIdList)) {
                            pointService.batchPointBindDevice(pointIdList, BindTypeEnum.NO.getType());
                        }
                    }
                }
            });
        } catch (Exception e) {
            log.error("指令下发属性解析异常--->error,entityDTO={}", JsonUtils.toJsonString(messageView), e);
        }
        return ConsumeResult.SUCCESS;

    }
    /**
     * 解析消息体为实体类
     */
    private DeviceGroupChangeModel parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, DeviceGroupChangeModel.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
}
