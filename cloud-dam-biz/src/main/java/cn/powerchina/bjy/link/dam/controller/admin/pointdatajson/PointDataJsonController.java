package cn.powerchina.bjy.link.dam.controller.admin.pointdatajson;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.*;
import cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo.PointParamTableRespVO;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import cn.powerchina.bjy.link.dam.service.pointdataimport.PointDataImportService;
import cn.powerchina.bjy.link.dam.service.pointdatajson.PointDataJsonService;
import com.alibaba.excel.EasyExcel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测点数据json")
@RestController
@RequestMapping("/dam/point/data/json")
@Validated
public class PointDataJsonController {

    @Resource
    private PointDataJsonService pointDataJsonService;

    @Resource
    private InstrumentModelService instrumentModelService;

    @Resource
    private PointDataImportService pointDataImportService;

    @PostMapping("/create")
    @Operation(summary = "创建人工录入测点数据")
//    @PreAuthorize("@ss.hasPermission('dam:point-data-json:create')")
    public CommonResult<Long> createPointDataJson(@Valid @RequestBody PointDataJsonSaveReqVO createReqVO) {
        return success(pointDataJsonService.createPointDataJson(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新人工录入测点数据")
//    @PreAuthorize("@ss.hasPermission('dam:point-data-json:update')")
    public CommonResult<Boolean> updatePointDataJson(@Valid @RequestBody PointDataJsonSaveReqVO updateReqVO) {
        pointDataJsonService.updatePointDataJson(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除人工录入测点数据")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:point-data-json:delete')")
    public CommonResult<Boolean> deletePointDataJson(@Valid @RequestBody PointDataJsonSaveReqVO createReqVO) {
        pointDataJsonService.deletePointDataJson(createReqVO);
        return success(true);
    }

    @PostMapping("/get")
    @Operation(summary = "获得人工录入测点数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:point-data-json:query')")
    public CommonResult<PointDataJsonRespVO> getPointDataJson(@RequestBody PointDataJsonSaveReqVO createReqVO) {
        PointDataJsonRespVO pointDataJson = pointDataJsonService.getPointDataJson(createReqVO);
        return success(BeanUtils.toBean(pointDataJson, PointDataJsonRespVO.class));
    }

    @PutMapping("/manual/calculate")
    @Operation(summary = "计算人工录入的数据")
//    @PreAuthorize("@ss.hasPermission('dam:point-data-json:query')")
    public CommonResult<List<PointInstrumentModelJsonVO>> calculate(@Valid @RequestBody PointDataJsonSaveReqVO createReqVO) {
        List<PointInstrumentModelJsonVO> resultModelList = pointDataJsonService.calculatePointFormula(createReqVO);
        return success(resultModelList);
    }


    @PutMapping("/recalculate")
    @Operation(summary = "重新计算")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:point-data-json:query')")
    public CommonResult<Long> recalculate(@Valid @RequestBody PointDataJsonSaveReqVO createReqVO) {
        pointDataJsonService.recalculate(createReqVO);
        return success(1L);
    }

    @GetMapping("/batch/calculate")
    @Operation(summary = "批量计算")
//    @PreAuthorize("@ss.hasPermission('dam:point-data-json:query')")
    public CommonResult<Long> batchCalculate(@Valid PointDataJsonPageReqVO pageReqVO) {
        String message = pointDataJsonService.batchCalculate(pageReqVO);
        if (Objects.isNull(message) || "".equals(message)) {
            return success(null);
        } else {
            CommonResult<Long> success = success(null);
            success.setMsg(message);
            success.setCode(500);
            return success;
        }
    }

    @GetMapping("/page")
    @Operation(summary = "获得测点数据json分页")
//    @PreAuthorize("@ss.hasPermission('dam:point-data-json:query')")
    public CommonResult<PageResult<PointDataJsonRespVO>> getPointDataJsonPage(@Valid PointDataJsonPageReqVO pageReqVO) {
        PageResult<PointDataJsonRespVO> pageResult = pointDataJsonService.getPointDataJsonPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PointDataJsonRespVO.class));
    }

    @GetMapping("/table")
    @Operation(summary = "动态获取测点分量表头")
    // @PreAuthorize("@ss.hasPermission('dam:point-data-json:query')")
    @Parameter(name = "categoryId", description = "工程分类id(测点id)", required = true)
    @Parameter(name = "thingTypeList", description = "分量类型，为空默认查询原始和成果分量；；1：原始值，2：中间值，3：成果值", required = false)
    public CommonResult<List<PointParamTableRespVO>> getPointTable(Long categoryId, @RequestParam(value = "thingTypeList", required = false) List<Integer> thingTypeList) {
        List<PointParamTableRespVO> pointModelTable = instrumentModelService.getPointModelTable(categoryId, thingTypeList);
        return success(BeanUtils.toBean(pointModelTable, PointParamTableRespVO.class));
    }

    @GetMapping("/manual/table")
    @Operation(summary = "动态获取人工数据管理表头")
    // @PreAuthorize("@ss.hasPermission('dam:point-data-json:query')")
    @Parameter(name = "categoryId", description = "工程分类id(测点id)", required = true)
    public CommonResult<List<PointParamTableRespVO>> getManualPointTable(Long categoryId) {
        List<PointParamTableRespVO> pointModelTable = instrumentModelService.getManualPointTable(categoryId);
        return success(BeanUtils.toBean(pointModelTable, PointParamTableRespVO.class));
    }
    @GetMapping("/getCurrentTime")
    @Operation(summary = "获取当前时间")
    // @PreAuthorize("@ss.hasPermission('dam:point-data-json:query')")
    @Parameter(name = "categoryId", description = "工程分类id(测点id)", required = true)
    public CommonResult<List<LocalDateTime>> getCurrentTime(Long categoryId) {
        return success(instrumentModelService.getCurrentTime(categoryId));
    }

    @GetMapping("/information")
    @Operation(summary = "新建页面获取固定的基础信息")
    @Parameter(name = "categoryId", description = "工程分类id(测点id)", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:point-data-json:query')")
    public CommonResult<PointInformationVO> information(@RequestParam("categoryId") Long categoryId) {
        return success(pointDataJsonService.getInformationByPointId(categoryId));
    }

    @PutMapping("/reviewGet")
    @Operation(summary = "查询数据审核弹窗页面")
//    @PreAuthorize("@ss.hasPermission('dam:point-data-json:query')")
    public CommonResult<PointDataJsonReviewVO> getPointDataJsonReview(@Valid @RequestBody PointDataJsonSaveReqVO createReqVO) {
        PointDataJsonReviewVO reviewPointDataJson = pointDataJsonService.getReviewPointDataJson(createReqVO);
        return success(BeanUtils.toBean(reviewPointDataJson, PointDataJsonReviewVO.class));
    }

    @PutMapping("/reviewSave")
    @Operation(summary = "保存数据审核弹窗页面")
//    @PreAuthorize("@ss.hasPermission('dam:point-data-json:update')")
    public CommonResult<Boolean> reviewSave(@Valid @RequestBody PointDataJsonReviewVO updateReqVO) {
        pointDataJsonService.reviewSave(updateReqVO);
        return success(true);
    }

    @GetMapping("/export")
    @Operation(summary = "测点数据查询导出")
    public void export(HttpServletResponse response, PointDataJsonPageReqVO pageReqVO) {
        List<PointParamTableRespVO> dataTable = instrumentModelService.getPointModelTable(pageReqVO.getPointId(), pageReqVO.getThingTypeList());
        List<List<String>> tableHead = new ArrayList<List<String>>();
        dataTable.forEach(item -> {
            List<String> headModel = new ArrayList<String>();
            headModel.add(item.getLabelName());
            tableHead.add(headModel);
        });
        //数据
        List<List<String>> dataList = pointDataJsonService.getDataList(pageReqVO);
        try {
            response.setContentType("application/binary;charset=UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("测点数据.xlsx", StandardCharsets.UTF_8.name()));
            EasyExcel.write(response.getOutputStream())
                    // 这里放入动态头
                    .head(tableHead).sheet("数据")
                    // 当然这里数据也可以用 List<List<String>> 去传入
                    .doWrite(dataList);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.POINT_DATA_IMPORT_TEMPLATE_ERROR, e.getMessage());
        }
    }

}