package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description:
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "toolbox")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartToolboxVO {

    @Schema(description = "show")
    private Boolean show;

    @Schema(description = "feature")
    private PointDataDistributionChartToolboxFeatureVO feature;
}
