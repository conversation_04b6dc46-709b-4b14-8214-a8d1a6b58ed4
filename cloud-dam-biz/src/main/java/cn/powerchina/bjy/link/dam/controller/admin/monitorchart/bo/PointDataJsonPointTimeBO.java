package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.bo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 测点数据json检测时间
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/27
 */
@Data
public class PointDataJsonPointTimeBO {

    /**
     * 测点id
     */
    private Long pointId;

    /**
     * 最大检测时间
     */
    private LocalDateTime minTime;

    /**
     * 最小检测时间
     */
    private LocalDateTime maxTime;
}
