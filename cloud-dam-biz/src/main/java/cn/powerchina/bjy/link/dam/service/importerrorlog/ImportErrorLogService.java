package cn.powerchina.bjy.link.dam.service.importerrorlog;

import java.util.*;
import jakarta.validation.*;
import cn.powerchina.bjy.link.dam.controller.admin.importerrorlog.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importerrorlog.ImportErrorLogDO;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;

/**
 * 错误日志 Service 接口
 *
 * <AUTHOR>
 */
public interface ImportErrorLogService {

    /**
     * 创建错误日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createImportErrorLog(@Valid ImportErrorLogSaveReqVO createReqVO);

    /**
     * 更新错误日志
     *
     * @param updateReqVO 更新信息
     */
    void updateImportErrorLog(@Valid ImportErrorLogSaveReqVO updateReqVO);

    /**
     * 删除错误日志
     *
     * @param id 编号
     */
    void deleteImportErrorLog(Long id);

    /**
    * 批量删除错误日志
    *
    * @param ids 编号
    */
    void deleteImportErrorLogListByIds(List<Long> ids);

    /**
     * 获得错误日志
     *
     * @param id 编号
     * @return 错误日志
     */
    ImportErrorLogDO getImportErrorLog(Long id);

    /**
     * 获得错误日志分页
     *
     * @param pageReqVO 分页查询
     * @return 错误日志分页
     */
    PageResult<ImportErrorLogDO> getImportErrorLogPage(ImportErrorLogPageReqVO pageReqVO);

}