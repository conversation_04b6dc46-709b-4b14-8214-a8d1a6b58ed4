package cn.powerchina.bjy.link.dam.controller.admin.importerrorlog;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;

import cn.powerchina.bjy.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static cn.powerchina.bjy.cloud.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.powerchina.bjy.link.dam.controller.admin.importerrorlog.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importerrorlog.ImportErrorLogDO;
import cn.powerchina.bjy.link.dam.service.importerrorlog.ImportErrorLogService;

@Tag(name = "管理后台 - 错误日志")
@RestController
@RequestMapping("/dam/import-error-log")
@Validated
public class ImportErrorLogController {

    @Resource
    private ImportErrorLogService importErrorLogService;

    @PostMapping("/create")
    @Operation(summary = "创建错误日志")
    @PreAuthorize("@ss.hasPermission('dam:import-error-log:create')")
    public CommonResult<Long> createImportErrorLog(@Valid @RequestBody ImportErrorLogSaveReqVO createReqVO) {
        return success(importErrorLogService.createImportErrorLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新错误日志")
    @PreAuthorize("@ss.hasPermission('dam:import-error-log:update')")
    public CommonResult<Boolean> updateImportErrorLog(@Valid @RequestBody ImportErrorLogSaveReqVO updateReqVO) {
        importErrorLogService.updateImportErrorLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除错误日志")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('dam:import-error-log:delete')")
    public CommonResult<Boolean> deleteImportErrorLog(@RequestParam("id") Long id) {
        importErrorLogService.deleteImportErrorLog(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除错误日志")
                @PreAuthorize("@ss.hasPermission('dam:import-error-log:delete')")
    public CommonResult<Boolean> deleteImportErrorLogList(@RequestParam("ids") List<Long> ids) {
        importErrorLogService.deleteImportErrorLogListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得错误日志")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('dam:import-error-log:query')")
    public CommonResult<ImportErrorLogRespVO> getImportErrorLog(@RequestParam("id") Long id) {
        ImportErrorLogDO importErrorLog = importErrorLogService.getImportErrorLog(id);
        return success(BeanUtils.toBean(importErrorLog, ImportErrorLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得错误日志分页")
//    @PreAuthorize("@ss.hasPermission('dam:import-error-log:query')")
    public CommonResult<PageResult<ImportErrorLogRespVO>> getImportErrorLogPage(@Valid ImportErrorLogPageReqVO pageReqVO) {
        PageResult<ImportErrorLogDO> pageResult = importErrorLogService.getImportErrorLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ImportErrorLogRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出错误日志 Excel")
    @PreAuthorize("@ss.hasPermission('dam:import-error-log:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportImportErrorLogExcel(@Valid ImportErrorLogPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ImportErrorLogDO> list = importErrorLogService.getImportErrorLogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "错误日志.xls", "数据", ImportErrorLogRespVO.class,
                        BeanUtils.toBean(list, ImportErrorLogRespVO.class));
    }

}