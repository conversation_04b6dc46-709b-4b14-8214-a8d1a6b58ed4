package cn.powerchina.bjy.link.dam.service.importtask;

import java.util.*;
import jakarta.validation.*;
import cn.powerchina.bjy.link.dam.controller.admin.importtask.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importtask.ImportTaskDO;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;

/**
 * 导入任务 Service 接口
 *
 * <AUTHOR>
 */
public interface ImportTaskService {

    /**
     * 创建导入任务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createImportTask(@Valid ImportTaskSaveReqVO createReqVO);

    /**
     * 更新导入任务
     *
     * @param updateReqVO 更新信息
     */
    void updateImportTask(@Valid ImportTaskSaveReqVO updateReqVO);

    /**
     * 删除导入任务
     *
     * @param id 编号
     */
    void deleteImportTask(Long id);

    /**
    * 批量删除导入任务
    *
    * @param ids 编号
    */
    void deleteImportTaskListByIds(List<Long> ids);

    /**
     * 获得导入任务
     *
     * @param id 编号
     * @return 导入任务
     */
    ImportTaskDO getImportTask(Long id);

    /**
     * 获得导入任务分页
     *
     * @param pageReqVO 分页查询
     * @return 导入任务分页
     */
    PageResult<ImportTaskDO> getImportTaskPage(ImportTaskPageReqVO pageReqVO);

}