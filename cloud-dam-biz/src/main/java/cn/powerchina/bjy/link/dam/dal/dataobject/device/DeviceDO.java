package cn.powerchina.bjy.link.dam.dal.dataobject.device;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 大坝设备 DO
 *
 * <AUTHOR>
 */
@TableName("dam_device")
@KeySequence("dam_device_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 父设备号（网关）
     */
    private String parentCode;
    /**
     * 父设备名称
     */
    private String parentName;
    /**
     * 父设备产品编码
     */
    private String parentProductCode;
    /**
     * 父设备唯一标识
     */
    private String parentSerial;
    /**
     * 设备编码
     */
    private String deviceCode;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * mcu通道编码
     */
    private String mcuChannel;
    /**
     * 设备唯一标识
     */
    private String deviceSerial;
    /**
     * 最后上线时间
     */
    private LocalDateTime lastUpTime;
    /**
     * 测站id
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long stationId;
    /**
     * 测点id
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long pointId;
    /**
     * 策略id
     */
    private Long strategyId;
    /**
     * 设备电压
     */
    private String deviceVoltage;
    /**
     * 节点类型(0直连，1网关，2网关子设备）
     */
    private Integer nodeType;

    /**
     * 连接状态（0-离线；1-在线；）
     */
    private Integer linkState;

    /**
     * 是否绑定测点，0：未绑定，1：已绑定
     */
    private Integer bindType;

    /**
     * 设备温度
     */
    private String deviceTemperature;

    /**
     * 设备时钟
     */
    private LocalDateTime deviceClock;

    /**
     * 设备电压上报时间
     */
    private LocalDateTime voltageTime;

    /**
     * 设备温度上报时间
     */
    private LocalDateTime temperatureTime;

    /**
     * 设备时钟上报时间
     */
    private LocalDateTime clockTime;
}