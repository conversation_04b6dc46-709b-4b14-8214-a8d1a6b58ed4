package cn.powerchina.bjy.link.dam.dal.dataobject.importconfig;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;

/**
 * 导入配置 DO
 *
 * <AUTHOR>
 */
@TableName("dam_import_config")
@KeySequence("dam_import_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportConfigDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 导入信息id
     */
    private Long importId;
    /**
     * 工作表名称
     */
    private String sheetName;
    /**
     * 测点id
     */
    private Long pointId;
    /**
     * 以自动化监测数据存储,0否，1是
     */
    private Integer isAutomation;
    /**
     * 分量id
     */
    private Long instrumentModelId;
    /**
     * 分量标识符
     */
    private String thingIdentity;
    /**
     * 分量名称-列
     */
    private Integer thingColumn;


}