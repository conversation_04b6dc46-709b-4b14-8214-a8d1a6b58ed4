package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Author: yangjingt<PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "feature")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartToolboxFeatureVO {

    @Schema(description = "dataZoom")
    private PointDataDistributionChartToolboxFeatureDataZoomVO dataZoom;
    
}
