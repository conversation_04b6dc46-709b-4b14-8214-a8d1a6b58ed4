package cn.powerchina.bjy.link.dam.service.instrumentmodel;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.bo.SelectBO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo.PointParamTableRespVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import jakarta.validation.Valid;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 仪器类型-测量分量 Service 接口
 *
 * <AUTHOR>
 */
public interface InstrumentModelService {

    /**
     * 创建仪器类型-测量分量
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInstrumentModel(@Valid InstrumentModelSaveReqVO createReqVO);

    /**
     * 更新仪器类型-测量分量
     *
     * @param updateReqVO 更新信息
     */
    void updateInstrumentModel(@Valid InstrumentModelSaveReqVO updateReqVO);

    /**
     * 删除仪器类型-测量分量
     *
     * @param id 编号
     */
    void deleteInstrumentModel(Long id);

    /**
     * 获得仪器类型-测量分量
     *
     * @param id 编号
     * @return 仪器类型-测量分量
     */
    InstrumentModelDO getInstrumentModel(Long id);

    /**
     * 获得仪器类型-测量分量分页
     *
     * @param pageReqVO 分页查询
     * @return 仪器类型-测量分量分页
     */
    PageResult<InstrumentModelRespVO> getInstrumentModelPage(InstrumentModelPageReqVO pageReqVO);

    /**
     * 根据仪器id获得IOT产品属性标识列表
     *
     * @param instrumentId
     * @return
     */
    List<SelectBO> selectProductIdentityList(Long instrumentId);


    List<SelectBO> getProductCodeIdentity(String productCode);

    /**
     * 获取仪器类型
     *
     * @param id
     */
    InstrumentModelDO validateInstrumentModelExists(Long id);

    /**
     * 根据分量类型查询分量列表
     *
     * @param instrumentId
     * @param typeList
     * @return
     */
    List<InstrumentModelDO> getModelByThingType(Long instrumentId, List<Integer> typeList);

    /**
     * 根据仪器类型id查询测量分量
     *
     * @param instrumentId
     * @return
     */
    List<InstrumentModelDO> getModelByInstrumentId(Long instrumentId);

    /**
     * 根据测点id获取测量分量列表
     *
     * @param pointId
     * @return
     */
    List<InstrumentModelDO> getInstrumentModelList(Long pointId);

    /**
     * 根据iot属性标识符查询测量分量
     *
     * @param instrumentId
     * @return
     */
    InstrumentModelDO getModelByInstrumentIdAndThingIdentityIot(Long instrumentId, String thingIdentityIot);

    /**
     * 验证是否存在相同标识符
     *
     * @param id
     * @param instrumentId
     * @param thingIdentity
     */
    void validateThingIdentityExists(Long id, Long instrumentId, String thingIdentity);

    /**
     * 查询引用标识符的公式列表
     *
     * @param instrumentId
     * @param thingIdentity
     * @return
     */
    List<InstrumentModelDO> getListUsingByFormula(Long instrumentId, String thingIdentity);

    /**
     * 根据测点id获取分量的动态表头
     *
     * @param pointId
     * @return
     */
    List<PointParamTableRespVO> getPointModelTable(Long pointId, List<Integer> thingTypeList);

    /**
     * 动态获取人工数据管理表头
     *
     * @param pointId
     * @return
     */
    List<PointParamTableRespVO> getManualPointTable(Long pointId);

    List<LocalDateTime> getCurrentTime(Long pointId);

    /**
     * 验证分量名称重复
     *
     * @param id
     * @param instrumentId
     * @param name
     */
    void validateNameExists(Long id, Long instrumentId, String name);

    void updateListInstrumentModel(List<InstrumentModelSaveReqVO> updateReqVO);

    /**
     * 获得仪器类型-测量分量列表
     *
     * @param
     * @return 仪器类型-测量分量
     */
    List<InstrumentModelRespVO> getInstrumentModelLists(InstrumentModelRespVO InstrumentModelRespVO);

    /**
     * 获取动态分量列表
     *
     * @param pointId
     * @param thingTypeList
     * @return
     */
    List<PointParamTableRespVO> getModelTable(Long pointId, List<Integer> thingTypeList);

    /**
     * 根据id列表查询
     *
     * @param idList
     * @return
     */
    List<InstrumentModelDO> listByIdList(List<Long> idList);
}