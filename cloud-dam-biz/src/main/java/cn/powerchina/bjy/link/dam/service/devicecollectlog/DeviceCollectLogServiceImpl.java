package cn.powerchina.bjy.link.dam.service.devicecollectlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog.vo.DeviceCollectLogPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog.vo.DeviceCollectLogSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.devicecollectlog.DeviceCollectLogDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.devicestrategy.DeviceStrategyDO;
import cn.powerchina.bjy.link.dam.dal.mysql.devicecollectlog.DeviceCollectLogMapper;
import cn.powerchina.bjy.link.dam.enums.*;
import cn.powerchina.bjy.link.dam.mq.RocketMQv5Client;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.dam.service.devicestrategy.DeviceStrategyService;
import cn.powerchina.bjy.link.dam.util.MyDateUtils;
import cn.powerchina.bjy.link.dam.util.MyStringUtils;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.model.DeviceCommandModel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.DEVICE_COLLECT_LOG_NOT_EXISTS;

/**
 * 网关设备采集日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DeviceCollectLogServiceImpl implements DeviceCollectLogService {

    @Resource
    private DeviceCollectLogMapper deviceCollectLogMapper;

    @Autowired
    private DeviceStrategyService deviceStrategyService;

    @Resource
    private ThreadPoolTaskExecutor damThreadPoolTaskExecutor;

    @Autowired
    @Lazy
    private DeviceService deviceService;


    @Resource
    private RocketMQv5Client rocketMQv5Client;
    @Override
    @Transactional
    public void createDeviceCollectLog(DeviceCollectLogSaveReqVO createReqVO) {
        //删除未执行的策略
        int delete = deviceCollectLogMapper.delete(new LambdaQueryWrapperX<DeviceCollectLogDO>()
                .eq(DeviceCollectLogDO::getProjectId, createReqVO.getProjectId())
                .eq(DeviceCollectLogDO::getDeviceCode, createReqVO.getDeviceCode())
                .eq(DeviceCollectLogDO::getCollectStatus, DeviceCollectStatusEnum.CREATE.getType()));
        log.info("delete not send device collect log size {}", delete);
        //生成采集策略时间
        List<Date> collectTimeList = generateCollectTime(createReqVO.getStrategyId());
        if (!CollectionUtils.isEmpty(collectTimeList)) {
            collectTimeList.sort(Comparator.comparing(Date::getTime));
            // 插入
            List<DeviceCollectLogDO> collectLogDOList = new ArrayList<>();
            for (int i = 0; i < collectTimeList.size(); i++) {
                DeviceCollectLogDO collectLogDO = new DeviceCollectLogDO();
                collectLogDO.setProjectId(createReqVO.getProjectId());
                collectLogDO.setDeviceCode(createReqVO.getDeviceCode());
                collectLogDO.setMsgId(MyStringUtils.generateMsgId());
                collectLogDO.setCollectStatus(DeviceCollectStatusEnum.CREATE.getType());
                collectLogDO.setCollectTime(MyDateUtils.transportDateToLocalDateTime(collectTimeList.get(i)));
                collectLogDO.setStrategyId(createReqVO.getStrategyId());
                collectLogDO.setLastFlag(i == collectTimeList.size() - 1 ? CollectLastFlagTypeEnum.YES.getType() : CollectLastFlagTypeEnum.NO.getType());
                collectLogDOList.add(collectLogDO);
            }
            //批量插入
            deviceCollectLogMapper.insertBatch(collectLogDOList);
        }
    }

    /**
     * 生成采集时间列表
     *
     * @param strategyId
     * @return
     */
    private List<Date> generateCollectTime(Long strategyId) {
        List<Date> timeList = new ArrayList<>();
        DeviceStrategyDO strategyDO = deviceStrategyService.getDeviceStrategy(strategyId);
        int addDays = 0;
        do {
            timeList.addAll(generateCollectTimeDetail(MyDateUtils.getFormatDate(DateUtils.addDays(new Date(), addDays++),
                    "yyyy-MM-dd"), strategyDO.getStrategyType(), strategyDO.getTimeInterval(), strategyDO.getTimePoint()));
        } while (CollectionUtils.isEmpty(timeList));
        return timeList;
    }

    /**
     * 生成具体采集时间列表
     *
     * @param dayStr
     * @param timePoint
     * @return
     */
    private List<Date> generateCollectTimeDetail(String dayStr, Integer strategyType, String timeInterval, String timePoint) {
        Date startTime = MyDateUtils.getParseDate(dayStr, "yyyy-MM-dd");
        Date endTime = DateUtils.addDays(startTime, 1);
        List<Date> timeList = new ArrayList<>();
        if (Objects.equals(strategyType, StrategyTypeEnum.POINT_TIME.getType())) {
            //08:10,09:10,10:23
            String[] timePointList = timePoint.split(",");
            for (String timePointItem : timePointList) {
                timeList.add(MyDateUtils.getParseDate(dayStr + " " + timePointItem, "yyyy-MM-dd HH:mm"));
            }
        } else if (Objects.equals(strategyType, StrategyTypeEnum.INTERVAL_TIME.getType())) {
            timeInterval = timeInterval.replace("小时", "#").replace("分", "#").replace("秒", "#");
            String[] times = timeInterval.split("#");
            while (true) {
                startTime = DateUtils.addHours(startTime, Integer.parseInt(times[0]));
                startTime = DateUtils.addMinutes(startTime, Integer.parseInt(times[1]));
                startTime = DateUtils.addSeconds(startTime, Integer.parseInt(times[2]));
                if (startTime.compareTo(endTime) > 0) {
                    break;
                }
                timeList.add(startTime);
            }
        } else if (Objects.equals(strategyType, StrategyTypeEnum.INTERVAL_DAY.getType())) {
            String[] timeMomentList = timePoint.split(",");
            Date targetDate = DateUtils.addDays(startTime, Integer.parseInt(timeInterval.replace("天", "")));
            for (String timeMomentItem : timeMomentList) {
                timeList.add(MyDateUtils.getParseDate(MyDateUtils.getFormatDate(targetDate, "yyyy-MM-dd") + " " + timeMomentItem, "yyyy-MM-dd HH:mm"));
            }
        }
        timeList.removeIf(item -> item.compareTo(new Date()) <= 0);
        return timeList;
    }

    @Override
    public void updateDeviceCollectLog(DeviceCollectLogSaveReqVO updateReqVO) {
        // 校验存在
        validateDeviceCollectLogExists(updateReqVO.getId());
        // 更新
        DeviceCollectLogDO updateObj = BeanUtils.toBean(updateReqVO, DeviceCollectLogDO.class);
        deviceCollectLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteDeviceCollectLog(Long id) {
        // 校验存在
        validateDeviceCollectLogExists(id);
        // 删除
        deviceCollectLogMapper.deleteById(id);
    }

    private void validateDeviceCollectLogExists(Long id) {
        if (deviceCollectLogMapper.selectById(id) == null) {
            throw exception(DEVICE_COLLECT_LOG_NOT_EXISTS);
        }
    }

    @Override
    public DeviceCollectLogDO getDeviceCollectLog(Long id) {
        return deviceCollectLogMapper.selectById(id);
    }

    @Override
    public PageResult<DeviceCollectLogDO> getDeviceCollectLogPage(DeviceCollectLogPageReqVO pageReqVO) {
        return deviceCollectLogMapper.selectPage(pageReqVO);
    }

    @Override
    public void runDeviceCollectLog() {
        CompletableFuture.runAsync(() -> {
            log.info("DeviceCollectLogService---start");
            int start = 0, size = 100;
            AtomicInteger total = new AtomicInteger();
            Long id = 0L;
            Date currentTime = new Date();
            do {
                List<DeviceCollectLogDO> deviceCollectLogDOList = deviceCollectLogMapper.selectList(new LambdaQueryWrapperX<DeviceCollectLogDO>()
                        .eq(DeviceCollectLogDO::getCollectStatus, DeviceCollectStatusEnum.CREATE.getType())
                        .le(DeviceCollectLogDO::getCollectTime, currentTime)
                        .gt(DeviceCollectLogDO::getId, id)
                        .orderByAsc(DeviceCollectLogDO::getId)
                        .last("limit " + start + "," + size));
                if (!CollectionUtils.isEmpty(deviceCollectLogDOList)) {
                    deviceCollectLogDOList.forEach(item -> {
                        if (sendDeviceCollectTimeToIOT(item.getId(), item.getCollectStatus()) > 0) {
                            //发送iot消息
                            sendMqCollect(item.getProjectId(), item.getDeviceCode());
                            log.info("发送到iot平台，deviceCode={},projectId={}", item.getDeviceCode(), item.getProjectId());
                            total.getAndIncrement();
                        }
                        if (Objects.equals(item.getLastFlag(), CollectLastFlagTypeEnum.YES.getType())) {
                            //生成新的计划
                            createDeviceCollectLog(DeviceCollectLogSaveReqVO.builder().deviceCode(item.getDeviceCode())
                                    .projectId(item.getProjectId()).strategyId(item.getStrategyId()).build());
                        }
                    });
                }
                if (deviceCollectLogDOList.size() < size) {
                    break;
                }
                id = deviceCollectLogDOList.get(deviceCollectLogDOList.size() - 1).getId();
            } while (true);
            log.info("DeviceCollectLogService---end,共分发了{}个执行计划", total.get());
        }, damThreadPoolTaskExecutor);
    }

    /**
     * 发送iot消息，采集信息
     */
    private void sendMqCollect(Long projectId, String deviceCode) {
        try {
            DeviceDO deviceDO = deviceService.getDeviceDOByProjectIdAndDeviceCode(projectId, deviceCode);
            if (Objects.isNull(deviceDO)) {
                throw exception(ErrorCodeConstants.DEVICE_NOT_EXISTS);
            }
            DeviceCommandModel deviceCommandModel = new DeviceCommandModel();
            deviceCommandModel.setDeviceCode(deviceCode);
            deviceCommandModel.setMcuChannelList(Objects.equals(deviceDO.getNodeType(), NodeTypeEnum.EDGE_SUB.getType()) ? List.of(deviceDO.getMcuChannel()) : null);
            rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DAM_COLLECT_COMMAND, deviceCommandModel,  IotTopicConstant.GROUP_DAM_COLLECT_COMMAND);
        } catch (Exception e) {
            log.error("send iot commend error {}", e.getMessage());
        }
    }

    /**
     * 修改收集状态为下发
     *
     * @param id
     * @param collectStatus
     * @return
     */
    private int sendDeviceCollectTimeToIOT(Long id, Integer collectStatus) {
        return deviceCollectLogMapper.updateCollectLogToSend(id, collectStatus, DeviceCollectStatusEnum.SEND.getType(), new Date());
    }

}