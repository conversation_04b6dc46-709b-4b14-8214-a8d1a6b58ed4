package cn.powerchina.bjy.link.dam.controller.admin.importnode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(description = "管理后台 - 节点信息新增/修改 Request VO")
@Data
public class ImportNodeSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21443")
    private Long id;

    @Schema(description = "项目id", example = "15937")
    private Long projectId;

    @Schema(description = "父节点id", example = "19571")
    private Long parentId;

    @Schema(description = "节点名称", example = "李四")
    private String name;

    @Schema(description = "节点层级")
    private Long hierarchy;

}