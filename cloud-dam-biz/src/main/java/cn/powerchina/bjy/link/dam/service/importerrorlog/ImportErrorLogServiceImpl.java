package cn.powerchina.bjy.link.dam.service.importerrorlog;

import cn.hutool.core.collection.CollUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.link.dam.controller.admin.importtask.vo.ImportTaskPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importnode.ImportNodeDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importtask.ImportTaskDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.mysql.importnode.ImportNodeMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.point.PointMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import cn.powerchina.bjy.link.dam.controller.admin.importerrorlog.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importerrorlog.ImportErrorLogDO;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;

import cn.powerchina.bjy.link.dam.dal.mysql.importerrorlog.ImportErrorLogMapper;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils.convertList;
import static cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils.diffList;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 错误日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ImportErrorLogServiceImpl implements ImportErrorLogService {

    @Resource
    private ImportErrorLogMapper importErrorLogMapper;
    @Resource
    private ImportNodeMapper importNodeMapper;
    @Resource
    private PointMapper pointMapper;

    @Override
    public Long createImportErrorLog(ImportErrorLogSaveReqVO createReqVO) {
        // 插入
        ImportErrorLogDO importErrorLog = BeanUtils.toBean(createReqVO, ImportErrorLogDO.class);
        importErrorLogMapper.insert(importErrorLog);

        // 返回
        return importErrorLog.getId();
    }

    @Override
    public void updateImportErrorLog(ImportErrorLogSaveReqVO updateReqVO) {
        // 校验存在
        validateImportErrorLogExists(updateReqVO.getId());
        // 更新
        ImportErrorLogDO updateObj = BeanUtils.toBean(updateReqVO, ImportErrorLogDO.class);
        importErrorLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteImportErrorLog(Long id) {
        // 校验存在
        validateImportErrorLogExists(id);
        // 删除
        importErrorLogMapper.deleteById(id);
    }

    @Override
    public void deleteImportErrorLogListByIds(List<Long> ids) {
        // 删除
        importErrorLogMapper.deleteBatchIds(ids);
    }


    private void validateImportErrorLogExists(Long id) {
        if (importErrorLogMapper.selectById(id) == null) {
//            throw exception(IMPORT_ERROR_LOG_NOT_EXISTS);
        }
    }

    @Override
    public ImportErrorLogDO getImportErrorLog(Long id) {
        return importErrorLogMapper.selectById(id);
    }

    @Override
    public PageResult<ImportErrorLogRespVO> getImportErrorLogPage(ImportErrorLogPageReqVO pageReqVO) {
        if (!Objects.nonNull(pageReqVO.getNodeId())) {
            throw exception(new ErrorCode(500, "节点不能为空"));
        }
        if (0 == pageReqVO.getNodeId()) {
            pageReqVO.setNodeId(null);
            PageResult<ImportErrorLogDO> importErrorLogDOPageResult = importErrorLogMapper.selectPage(pageReqVO);
            PageResult<ImportErrorLogRespVO> importErrorLogRespVO = BeanUtils.toBean(importErrorLogDOPageResult, ImportErrorLogRespVO.class);

            // 使用Stream API处理列表
            importErrorLogRespVO.setList(importErrorLogRespVO.getList().stream()
                    .peek(vo -> {
                        PointDO pointDO = pointMapper.selectById(vo.getPointId());
                        vo.setPointCode(pointDO.getPointCode());
                        vo.setSheetName(vo.getSheetName().split("&@")[0]);
                    })
                    .sorted(Comparator.comparing(ImportErrorLogRespVO::getExecuteTime).reversed())
                    .collect(Collectors.toList()));

            return importErrorLogRespVO;
        }
        //根据测点id和项目id获得所有子节点数据
        // 查询直接子节点
        ImportNodeDO importNodeDO = importNodeMapper.selectOne(new LambdaQueryWrapper<ImportNodeDO>()
                .eq(ImportNodeDO::getId, pageReqVO.getNodeId()));
        List<ImportNodeDO> allChildNodes = getAllChildNodes(pageReqVO.getNodeId());
        allChildNodes.add(importNodeDO);
        long total = 0;
        List<ImportErrorLogDO> errorLogDOS = new ArrayList<>();
        // 遍历每个节点查询任务
        for (ImportNodeDO node : allChildNodes) {
            ImportErrorLogPageReqVO importErrorLogPageReqVO = BeanUtils.toBean(pageReqVO, ImportErrorLogPageReqVO.class);
            importErrorLogPageReqVO.setNodeId(node.getId());
            LambdaQueryWrapper<ImportErrorLogDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ImportErrorLogDO::getNodeId, node.getId());
            queryWrapper.eq(ImportErrorLogDO::getProjectId, pageReqVO.getProjectId());
            if (pageReqVO.getPointId() != null) {
                queryWrapper.eq(ImportErrorLogDO::getPointId, pageReqVO.getPointId());
            }
            if (pageReqVO.getTaskId() != null) {
                queryWrapper.eq(ImportErrorLogDO::getTaskId, pageReqVO.getTaskId());
            }
            if (pageReqVO.getFileName() != null) {
                queryWrapper.like(ImportErrorLogDO::getFileName, pageReqVO.getFileName());
            }
            List<ImportErrorLogDO> errorLogDOS1 = importErrorLogMapper.selectList(queryWrapper);
            if (errorLogDOS1.size()>0) {
                errorLogDOS.addAll(errorLogDOS1);
                total += errorLogDOS1.size();
            }
        }
        // 处理分页
        int pageSize = pageReqVO.getPageSize();
        int pageNo = pageReqVO.getPageNo();
        int fromIndex = (pageNo - 1) * pageSize;
        if (fromIndex >= errorLogDOS.size()) {
            return new PageResult<>(Collections.emptyList(), total);
        }
        int toIndex = Math.min(fromIndex + pageSize, errorLogDOS.size());
        List<ImportErrorLogDO> errorLogDOS1 = errorLogDOS.subList(fromIndex, toIndex);
        // 处理分页结果
        List<ImportErrorLogRespVO> importErrorLogRespVO = errorLogDOS1.stream()
                .map(errorLog -> {
                    ImportErrorLogRespVO vo = BeanUtils.toBean(errorLog, ImportErrorLogRespVO.class);
                    PointDO pointDO = pointMapper.selectById(errorLog.getPointId());
                    vo.setPointCode(pointDO.getPointCode());
                    vo.setSheetName(errorLog.getSheetName().split("&@")[0]);
                    return vo;
                })
                .sorted(Comparator.comparing(ImportErrorLogRespVO::getExecuteTime).reversed())
                .collect(Collectors.toList());

        return new PageResult<>(importErrorLogRespVO, total);
    }

    /**
     * 获取指定节点下的所有子节点（递归）
     *
     * @param parentId  父节点ID
     * @return 子节点列表
     */
    private List<ImportNodeDO> getAllChildNodes(Long parentId) {
        List<ImportNodeDO> result = new ArrayList<>();
        // 查询直接子节点
        List<ImportNodeDO> directChildren = importNodeMapper.selectList(new LambdaQueryWrapper<ImportNodeDO>()
                .eq(ImportNodeDO::getParentId, parentId));
        if (directChildren != null && !directChildren.isEmpty()) {
            result.addAll(directChildren);
            for (ImportNodeDO child : directChildren) {
                // 递归查询子节点的子节点
                result.addAll(getAllChildNodes( child.getId()));
            }
        }
        return result;
    }
}