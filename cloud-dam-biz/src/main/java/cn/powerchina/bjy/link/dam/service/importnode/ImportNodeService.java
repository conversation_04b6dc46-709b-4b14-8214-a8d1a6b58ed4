package cn.powerchina.bjy.link.dam.service.importnode;

import java.util.*;
import cn.powerchina.bjy.link.dam.controller.admin.importnode.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importnode.ImportNodeDO;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import jakarta.validation.Valid;

/**
 * 节点信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ImportNodeService {

    /**
     * 创建节点信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createImportNode(@Valid ImportNodeSaveReqVO createReqVO);

    /**
     * 更新节点信息
     *
     * @param updateReqVO 更新信息
     */
    void updateImportNode(@Valid ImportNodeSaveReqVO updateReqVO);

    /**
     * 删除节点信息
     *
     * @param id 编号
     */
    void deleteImportNode(Long id);

    /**
    * 批量删除节点信息
    *
    * @param ids 编号
    */
    void deleteImportNodeListByIds(List<Long> ids);

    /**
     * 获得节点信息
     *
     * @param id 编号
     * @return 节点信息
     */
    ImportNodeRespVO getNodeByProjectId(Long projectId,String projectName);

    /**
     * 获得节点信息分页
     *
     * @param pageReqVO 分页查询
     * @return 节点信息分页
     */
    PageResult<ImportNodeDO> getImportNodePage(ImportNodePageReqVO pageReqVO);

}