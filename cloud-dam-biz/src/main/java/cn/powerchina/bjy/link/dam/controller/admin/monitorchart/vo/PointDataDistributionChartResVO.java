package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description: 监测图形-分布图的响应
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-分布图响应")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartResVO {

    @Schema(description = "唯一标识")
    private Long id;

    private String instance;

    @Schema(description = "配置")
    private PointDataDistributionChartOptionVO option;
}
