package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "toolbox")
@Data
public class PointDataProcessLineTooboxVO {

    @Schema(description = "show")
    private Boolean show;

    @Schema(description = "feature")
    private PointDataProcessLineTooboxFeatureVO feature;

}