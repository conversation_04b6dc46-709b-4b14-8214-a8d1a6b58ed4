package cn.powerchina.bjy.link.dam.dal.dataobject.importconfig;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 导入配置 DO
 *
 * <AUTHOR>
 */
@TableName("dam_import_config_model")
@KeySequence("dam_import_config_model_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportConfigModelDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 导入信息id
     */
    private Long pointConfigId;
    /**
     * 以自动化监测数据存储,0否，1是
     */
    private Integer isAutomation;
    /**
     * 监测数据开始行
     */
    private Integer startLine;
    /**
     * 分量id
     */
    private Long instrumentModelId;
    /**
     * 分量标识符
     */
    private String thingIdentity;
    /**
     * 分量名称-列
     */
    private Integer thingColumn;


}