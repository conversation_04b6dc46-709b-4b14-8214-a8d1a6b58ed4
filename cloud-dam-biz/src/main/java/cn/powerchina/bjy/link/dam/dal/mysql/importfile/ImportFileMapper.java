package cn.powerchina.bjy.link.dam.dal.mysql.importfile;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.link.dam.dal.dataobject.importfile.ImportFileDO;
import org.apache.ibatis.annotations.Mapper;
import cn.powerchina.bjy.link.dam.controller.admin.importfile.vo.*;

/**
 * 导入信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ImportFileMapper extends BaseMapperX<ImportFileDO> {

    default PageResult<ImportFileDO> selectPage(ImportFilePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ImportFileDO>()
                .eqIfPresent(ImportFileDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ImportFileDO::getNodeId, reqVO.getNodeId())
                .likeIfPresent(ImportFileDO::getFileName, reqVO.getFileName())
                .eqIfPresent(ImportFileDO::getFilePath, reqVO.getFilePath())
                .eqIfPresent(ImportFileDO::getPointNumber, reqVO.getPointNumber())
                .betweenIfPresent(ImportFileDO::getUploadTime, reqVO.getUploadTime())
                .betweenIfPresent(ImportFileDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(ImportFileDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(ImportFileDO::getUploadNew, reqVO.getUploadNew())
                .betweenIfPresent(ImportFileDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ImportFileDO::getId));
    }

}