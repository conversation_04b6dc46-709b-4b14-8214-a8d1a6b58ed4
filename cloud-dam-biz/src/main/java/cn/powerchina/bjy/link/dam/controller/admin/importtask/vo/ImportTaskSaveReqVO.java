package cn.powerchina.bjy.link.dam.controller.admin.importtask.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 导入任务新增/修改 Request VO")
@Data
public class ImportTaskSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18172")
    private Long id;

    @Schema(description = "任务id", example = "28192")
    private Long taskId;

    @Schema(description = "文件名称", example = "李四")
    private String fileName;

    @Schema(description = "最新上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "任务执行时间")
    private LocalDateTime executeTime;

    @Schema(description = "已录入测值数")
    private Long uploadNumber;

    @Schema(description = "应录入测值数")
    private Long totalNumber;

    @Schema(description = "执行状态", example = "1")
    private Integer taskStatus;

}