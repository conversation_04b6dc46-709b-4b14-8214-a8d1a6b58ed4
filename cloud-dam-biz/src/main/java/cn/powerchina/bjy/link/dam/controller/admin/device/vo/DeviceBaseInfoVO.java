package cn.powerchina.bjy.link.dam.controller.admin.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备基本信息
 */
@Schema(description = "设备基本信息")
@Data
public class DeviceBaseInfoVO {

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "设备唯一标识")
    private String deviceSerial;

    @Schema(description = "设备id")
    private String deviceCode;

    @Schema(description = "所属产品")
    private String productName;

    @Schema(description = "连接状态（0-离线；1-在线；）")
    private Integer linkState;

    @Schema(description = "最后上线时间")
    private LocalDateTime lastUpTime;
}
