package cn.powerchina.bjy.link.dam.service.point;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointBO;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointInstrumentModelBO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.PointGroupSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.PointInstrumentPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.PointPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.PointSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import jakarta.validation.Valid;

import java.util.Date;
import java.util.List;

/**
 * 测点信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PointService {

    /**
     * 创建测点信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPoint(@Valid PointSaveReqVO createReqVO);

    /**
     * 更新测点信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePoint(@Valid PointSaveReqVO updateReqVO);

    /**
     * 删除测点信息
     *
     * @param id 编号
     */
    void deletePoint(Long id);

    /**
     * 校验测点是否存在
     *
     * @param id
     * @return
     */
    PointDO validatePointExists(Long id);

    /**
     * 获得测点信息
     *
     * @param id 编号
     * @return 测点信息
     */
    PointDO getPoint(Long id);

    /**
     * 获得测点信息分页
     *
     * @param pageReqVO 分页查询
     * @return 测点信息分页
     */
    PageResult<PointDO> getPointPage(PointPageReqVO pageReqVO);

    /**
     * 获得测点信息分页
     *
     * @param pageReqVO 分页查询
     * @return 测点信息分页
     */
    PageResult<PointInstrumentModelBO> getPointInstrumentModelBOPage(PointInstrumentPageReqVO pageReqVO);

    /**
     * 统计测点数量
     *
     * @param projectId
     * @param instrumentId
     * @param pointState
     * @param pointTypes
     * @param startTime
     * @return
     */
    Long countPointByProjectId(Long projectId, Long instrumentId, Integer pointState, List<Integer> pointTypes, Date startTime);

    /**
     * 根据工程分类id进行查找
     *
     * @param categoryId
     * @return
     */
    List<PointDO> getPointListByCategoryId(Long categoryId);

    /**
     * 根据仪器类型id进行查找
     *
     * @param instrumentId
     * @return
     */
    List<PointDO> getPointListByInstrumentId(Long instrumentId);

    /**
     * 获得测点信息分页
     *
     * @param pageReqVO
     * @return
     */
    PageResult<PointBO> getPointBOPage(PointPageReqVO pageReqVO);

    /**
     * 获得测点信息
     *
     * @param id
     * @return
     */
    PointBO getPointBO(Long id);

    /**
     * 根据工程分类id查找列表
     *
     * @param categoryId
     * @return
     */
    List<PointDO> getPointDOListByCategoryId(Long categoryId);

    /**
     * 保存分组测点
     *
     * @param reqVO
     */
    void saveGroupPoint(PointGroupSaveReqVO reqVO);

    /**
     * 测点修改绑定状态
     *
     * @param pointId
     * @param bindType
     */
    void pointBindDevice(Long pointId, Integer bindType);

    /**
     * 测点批量修改绑定状态
     *
     * @param pointIds
     * @param bindType
     */
    void batchPointBindDevice(List<Long> pointIds, Integer bindType);

    /**
     * 获取测点id
     *
     * @param projectId
     * @param pointId
     * @return
     */
    List<PointDO> getPointDOListByProjectId(Long projectId, Long pointId);

    /**
     * 根据测点编号，查询唯一的测点
     *
     * @param projectId
     * @param pointCode
     * @return
     */
    PointDO getPointByProjectIdAndPointCode(Long projectId, String pointCode);

    /**
     * 过滤测点
     *
     * @param pageReqVO
     * @param pointId
     * @return
     */
    PageResult<PointBO> getPointRemoveBOPage(PointPageReqVO pageReqVO,Long pointId);

    /**
     * 根据测点id集合查询测点信息
     *
     * @param idList
     * @return
     */
    List<PointDO> listByIdList(List<Long> idList);
}