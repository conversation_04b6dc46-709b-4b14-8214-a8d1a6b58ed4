package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 监测图形-分布图--图例
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "图例")
@Data
public class PointDataDistributionChartLegendVO {

    @Schema(description = "图例数据")
    private List<String> data;

    @Schema(description = "top")
    private String top;

    @Schema(description = "type")
    private String type;

    @Schema(description = "orient")
    private String orient;

    @Schema(description = "pageButtonItemGap")
    private Integer pageButtonItemGap;

}
