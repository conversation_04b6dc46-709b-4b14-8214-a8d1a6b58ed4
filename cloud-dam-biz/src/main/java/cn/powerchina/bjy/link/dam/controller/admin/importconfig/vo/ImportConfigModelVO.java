package cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class ImportConfigModelVO {
    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 导入信息id
     */
    private Long pointConfigId;
    /**
     * 以自动化监测数据存储,0否，1是
     */
    private Integer isAutomation;
    /**
     * 监测数据开始行
     */
    private Integer startLine;
    /**
     * 分量id
     */
    private Long instrumentModelId;
    /**
     * 分量标识符
     */
    private String thingIdentity;
    /**
     * 分量名称-列
     */
    private Integer thingColumn;


    /**
     * 分量标识符
     */
    private String instrumentModelName;
}
