package cn.powerchina.bjy.link.dam.dal.mysql.importconfig;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo.ImportConfigPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigModelDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 导入配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ImportConfigModelMapper extends BaseMapperX<ImportConfigModelDO> {

}