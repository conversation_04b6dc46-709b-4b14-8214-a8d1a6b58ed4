package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 监测图形-过程线--x轴
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "x轴")
@Data
public class PointDataProcessLineXaxisVO {

    @Schema(description = "类型")
    private String type;

    @Schema(description = "边界间隔")
    private Boolean boundaryGap;

    @Schema(description = "刻度")
    private PointDataProcessLineAxisLabelVO axisLabel;

    @ArraySchema(
            arraySchema = @Schema(description = "数据"),
            schema = @Schema(implementation = String.class)
    )
    private List<String> data;

    @Schema(description = "轴线")
    private PointDataProcessLineXaxisLineVO axisLine;

    @Schema(description = "位置")
    private String position;

    @Schema(description = "splitLine")
    private PointDataProcessLineXsplitLineVO splitLine;
}
