package cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 导入配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ImportConfigRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25562")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "导入信息id", example = "16655")
    @ExcelProperty("导入信息id")
    private Long importId;

    @Schema(description = "工作表名称", example = "芋艿")
    @ExcelProperty("工作表名称")
    private String sheetName;

    @Schema(description = "测点id", example = "22025")
    @ExcelProperty("测点id")
    private Long pointId;

    @Schema(description = "以自动化监测数据存储,0否，1是")
    @ExcelProperty("以自动化监测数据存储,0否，1是")
    private Boolean isAutomation;

    @Schema(description = "分量id", example = "22476")
    @ExcelProperty("分量id")
    private Long instrumentModelId;

    @Schema(description = "分量标识符")
    @ExcelProperty("分量标识符")
    private String thingIdentity;

    @Schema(description = "分量名称-列", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分量名称-列")
    private Integer thingColumn;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}