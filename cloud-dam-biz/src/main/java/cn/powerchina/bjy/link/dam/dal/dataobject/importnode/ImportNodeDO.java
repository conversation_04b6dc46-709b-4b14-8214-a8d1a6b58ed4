package cn.powerchina.bjy.link.dam.dal.dataobject.importnode;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;

/**
 * 节点信息 DO
 *
 * <AUTHOR>
 */
@TableName("dam_import_node")
@KeySequence("dam_import_node_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportNodeDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 父节点id
     */
    private Long parentId;
    /**
     * 节点名称
     */
    private String name;
    /**
     * 节点层级
     */
    private Long hierarchy;


}