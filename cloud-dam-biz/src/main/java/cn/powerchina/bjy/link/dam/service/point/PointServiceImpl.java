package cn.powerchina.bjy.link.dam.service.point;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointBO;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointInstrumentModelBO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.PointGroupSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.PointInstrumentPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.PointPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.PointSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo.ProjectCategoryLevelBO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectcategory.ProjectCategoryDO;
import cn.powerchina.bjy.link.dam.dal.mysql.point.PointMapper;
import cn.powerchina.bjy.link.dam.enums.BindTypeEnum;
import cn.powerchina.bjy.link.dam.enums.CategoryTypeEnum;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.service.instrument.InstrumentService;
import cn.powerchina.bjy.link.dam.service.pointdata.PointDataService;
import cn.powerchina.bjy.link.dam.service.projectcategory.ProjectCategoryService;
import cn.powerchina.bjy.link.dam.util.SnowFlakeUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.POINT_NOT_EXISTS;

/**
 * 测点信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PointServiceImpl implements PointService {

    @Resource
    private PointMapper pointMapper;

    @Autowired
    private ProjectCategoryService projectCategoryService;

    @Autowired
    private InstrumentService instrumentService;

    @Autowired
    private SnowFlakeUtil snowFlakeUtil;

    @Autowired
    @Lazy
    private PointDataService pointDataService;

    @Override
    public Long createPoint(PointSaveReqVO createReqVO) {
        createReqVO.setId(null);
        // 插入
        PointDO point = BeanUtils.toBean(createReqVO, PointDO.class);
        //校验测点编号存在
        validatePointCodeExists(createReqVO.getProjectId(), createReqVO.getId(), createReqVO.getPointCode());
        //检测添加的位置
        ProjectCategoryDO categoryDO = validatePointAddPosition(createReqVO.getCategoryId());
        //设置节点类型
        point.setCategoryType(categoryDO.getCategoryType());
        //设置仪器类型
        ProjectCategoryLevelBO categoryLevelBO = projectCategoryService.getProjectCategoryLevelBO(createReqVO.getCategoryId());
        point.setInstrumentId(categoryLevelBO.getInstrumentCategoryDO().getBusinessId());
        //设置工程结构id
        point.setStructCategoryId(categoryLevelBO.getStructCategoryDO().getId());
        //设置id
        point.setId(snowFlakeUtil.snowflakeId());
        pointMapper.insert(point);
        // 返回
        return point.getId();
    }

    @Override
    public void updatePoint(PointSaveReqVO updateReqVO) {
        // 校验存在
        validatePointExists(updateReqVO.getId());
        //校验测点编号存在
        validatePointCodeExists(updateReqVO.getProjectId(), updateReqVO.getId(), updateReqVO.getPointCode());
        //检测添加的位置
        validatePointAddPosition(updateReqVO.getCategoryId());
        PointDO updateObj = BeanUtils.toBean(updateReqVO, PointDO.class);
        //更新
        pointMapper.updateById(updateObj);
    }

    @Override
    public void deletePoint(Long id) {
        // 校验存在
        PointDO pointDO = validatePointExists(id);
        //如果已经绑定，需要先解绑
        if (Objects.nonNull(pointDO.getBindType()) && Objects.equals(pointDO.getBindType(), BindTypeEnum.YES.getType())) {
            throw exception(ErrorCodeConstants.POINT_DELETE_BIND_EXISTS);
        }
        //有监测数据了不能删除
        if (pointDataService.countByProjectIdAndPointId(pointDO.getProjectId(), pointDO.getId()).compareTo(0L) > 0) {
            throw exception(ErrorCodeConstants.POINT_DELETE_DATA_EXISTS);
        }
        // 删除
        pointMapper.deleteById(id);
    }

    /**
     * 校验测点是否存在
     *
     * @param id
     */
    @Override
    public PointDO validatePointExists(Long id) {
        PointDO pointDO = getPoint(id);
        if (Objects.isNull(pointDO)) {
            throw exception(POINT_NOT_EXISTS);
        }
        return pointDO;
    }

    /**
     * 检测添加的位置
     *
     * @param categoryId
     * @return
     */
    private ProjectCategoryDO validatePointAddPosition(Long categoryId) {
        ProjectCategoryDO categoryDO = projectCategoryService.getProjectCategory(categoryId);
        if (Objects.isNull(categoryDO) || (!Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())
                && !Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.GROUP.getType()))) {
            throw exception(ErrorCodeConstants.POINT_SELECT_LEAF_ERROR);
        }
        return categoryDO;
    }

    /**
     * 校验测点编号存在
     *
     * @param projectId
     * @param id
     * @param pointCode
     */
    private void validatePointCodeExists(Long projectId, Long id, String pointCode) {
        PointDO pointDO = pointMapper.selectOne(new LambdaQueryWrapperX<PointDO>().eq(PointDO::getPointCode, pointCode)
                .eq(PointDO::getProjectId, projectId).last("limit 1"));
        if (Objects.nonNull(pointDO) && (Objects.isNull(id) || !Objects.equals(id, pointDO.getId()))) {
            throw exception(ErrorCodeConstants.POINT_CODE_EXISTS);
        }
    }

    @Override
    public PointDO getPoint(Long id) {
        return pointMapper.selectById(id);
    }

    @Override
    public PageResult<PointDO> getPointPage(PointPageReqVO pageReqVO) {
        return pointMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<PointInstrumentModelBO> getPointInstrumentModelBOPage(PointInstrumentPageReqVO pageReqVO) {
        return pointMapper.selectPointInstrumentModelPage(pageReqVO);
    }

    @Override
    public Long countPointByProjectId(Long projectId, Long instrumentId, Integer pointState, List<Integer> pointTypes, Date startTime) {
        Long pointCount = pointMapper.selectCount(new LambdaQueryWrapperX<PointDO>()
                .eq(PointDO::getProjectId, projectId)
                .eqIfPresent(PointDO::getInstrumentId, instrumentId)
                .eqIfPresent(PointDO::getPointState, pointState)
                .inIfPresent(PointDO::getPointType, pointTypes)
                .geIfPresent(PointDO::getCreateTime, startTime));
        return Objects.isNull(pointCount) ? 0L : pointCount;
    }

    @Override
    public List<PointDO> getPointListByCategoryId(Long categoryId) {
        return pointMapper.selectList(new LambdaQueryWrapperX<PointDO>().eq(PointDO::getCategoryId, categoryId)
                .orderByAsc(PointDO::getId));
    }

    @Override
    public List<PointDO> getPointListByInstrumentId(Long instrumentId) {
        return pointMapper.selectList(new LambdaQueryWrapperX<PointDO>().eq(PointDO::getInstrumentId, instrumentId)
                .orderByAsc(PointDO::getId));
    }

    @Override
    public PageResult<PointBO> getPointBOPage(PointPageReqVO pageReqVO) {
        //查询工程结构树，只有仪器类型和分组可以点击
        ProjectCategoryDO categoryDO = projectCategoryService.getProjectCategory(pageReqVO.getCategoryId());
        if (Objects.isNull(categoryDO) || (!Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())
                && !Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.GROUP.getType()))) {
            throw exception(ErrorCodeConstants.POINT_SELECT_LEAF_ERROR);
        }
        //要查询的工程分类id集合
        List<Long> categoryIdList = new ArrayList<>();
        categoryIdList.add(pageReqVO.getCategoryId());
        //如果是仪器类型，需要找到下面分组的id
        if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())) {
            List<ProjectCategoryDO> categoryDOList = projectCategoryService.getProjectCategoryListByParentId(pageReqVO.getCategoryId(), false, false);
            if (!CollectionUtils.isEmpty(categoryDOList)) {
                categoryIdList.addAll(categoryDOList.stream().map(ProjectCategoryDO::getId).toList());
            }
        }
        //进行查询
        pageReqVO.setCategoryIdList(categoryIdList);
        PageResult<PointDO> pageResult = getPointPage(pageReqVO);
        //进行转换
        List<PointBO> pointBOList = pageResult.getList().stream().map(item -> {
            PointBO pointBO = new PointBO();
            org.springframework.beans.BeanUtils.copyProperties(item, pointBO);
            formatPointBO(item, pointBO, true);
            return pointBO;
        }).toList();
        return new PageResult<>(pointBOList, pageResult.getTotal());
    }

    @Override
    public PointBO getPointBO(Long id) {
        PointDO pointDO = getPoint(id);
        if (Objects.nonNull(pointDO)) {
            PointBO pointBO = new PointBO();
            org.springframework.beans.BeanUtils.copyProperties(pointDO, pointBO);
            formatPointBO(pointDO, pointBO, false);
            return pointBO;
        }
        return null;
    }

    @Override
    public List<PointDO> getPointDOListByCategoryId(Long categoryId) {
        return pointMapper.selectList(new LambdaQueryWrapperX<PointDO>().eq(PointDO::getCategoryId, categoryId)
                .orderByDesc(PointDO::getId));
    }

    @Override
    @Transactional
    public void saveGroupPoint(PointGroupSaveReqVO reqVO) {
        //查找节点，只有分组节点可以操作
        ProjectCategoryDO categoryDO = projectCategoryService.validateProjectCategoryExists(reqVO.getCategoryId());
        if (!Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.GROUP.getType())) {
            throw exception(ErrorCodeConstants.POINT_SELECT_GROUP_ERROR);
        }
        //查找父节点，仪器类型
        ProjectCategoryDO parentCategoryDO = projectCategoryService.getProjectCategoryParent(categoryDO.getId());
        //找出当前分组拥有的测点
        List<PointDO> pointDOListOrigin = Optional.ofNullable(getPointDOListByCategoryId(reqVO.getCategoryId())).orElse(new ArrayList<>());
        List<Long> pointIdOrigin = pointDOListOrigin.stream().map(PointDO::getId).toList();
        List<Long> pointIdSelect = Optional.ofNullable(reqVO.getPointIds()).orElse(new ArrayList<>());
        //查找出需要删除的，删除的就是归于仪器类型
        List<PointDO> pointDOListRemove = pointDOListOrigin.stream().filter(item -> !pointIdSelect.contains(item.getId())).peek(item -> {
            item.setCategoryId(parentCategoryDO.getId());
            item.setCategoryType(parentCategoryDO.getCategoryType());
        }).toList();
        //查找出需要新增的，新增的就是修改categoryId
        List<PointDO> pointDOListAdd = pointIdSelect.stream().filter(item -> !pointIdOrigin.contains(item)).map(item -> {
            PointDO pointDO = validatePointExists(item);
            pointDO.setCategoryId(categoryDO.getId());
            pointDO.setCategoryType(categoryDO.getCategoryType());
            return pointDO;
        }).toList();
        //批量修改
        if (!CollectionUtils.isEmpty(pointDOListRemove)) {
            pointMapper.updateBatch(pointDOListRemove);
        }
        if (!CollectionUtils.isEmpty(pointDOListAdd)) {
            pointMapper.updateBatch(pointDOListAdd);
        }
    }

    @Override
    public void pointBindDevice(Long pointId, Integer bindType) {
        PointDO pointDO = getPoint(pointId);
        if (Objects.nonNull(pointDO)) {
            pointDO.setBindType(bindType);
            pointMapper.updateById(pointDO);
        }
    }

    @Override
    @Transactional
    public void batchPointBindDevice(List<Long> pointIds, Integer bindType) {
        if (!CollectionUtils.isEmpty(pointIds)) {
            List<PointDO> pointDOList = new ArrayList<>();
            pointIds.forEach(item -> {
                PointDO pointDO = getPoint(item);
                if (Objects.nonNull(pointDO)) {
                    pointDO.setBindType(bindType);
                    pointDOList.add(pointDO);
                }
            });
            if (!CollectionUtils.isEmpty(pointDOList)) {
                pointMapper.updateBatch(pointDOList);
            }
        }
    }

    @Override
    public List<PointDO> getPointDOListByProjectId(Long projectId, Long pointId) {
        List<PointDO> pointDOList = pointMapper.selectList(new LambdaQueryWrapperX<PointDO>().eq(PointDO::getProjectId, projectId)
                .orderByDesc(PointDO::getId));
        return Objects.nonNull(pointId) ? pointDOList.stream().filter(item -> !Objects.equals(item.getId(), pointId)).toList() : pointDOList;
    }

    @Override
    public PointDO getPointByProjectIdAndPointCode(Long projectId, String pointCode) {
        PointDO pointDO = pointMapper.selectOne(new LambdaQueryWrapperX<PointDO>().eq(PointDO::getPointCode, pointCode)
                .eq(PointDO::getProjectId, projectId).last("limit 1"));
        return pointDO;
    }

    /**
     * 格式化pointBO
     *
     * @param pointDO
     * @param pointBO
     */
    private void formatPointBO(PointDO pointDO, PointBO pointBO, boolean allPathName) {
        //如果是分组，设置组别
        if (Objects.equals(pointDO.getCategoryType(), CategoryTypeEnum.GROUP.getType())) {
            pointBO.setCategoryGroupName(projectCategoryService.getProjectCategoryId(pointDO.getCategoryId()).getCategoryName());
        }
        //设置仪器类型名称
        InstrumentDO instrument = instrumentService.getInstrument(pointDO.getInstrumentId());
        if (Objects.nonNull(instrument)) {
            pointBO.setInstrumentName(instrument.getInstrumentName());
        }
        //设置工程结构名称
        if (allPathName) {
            pointBO.setStructCategoryName(projectCategoryService.getAllPathCategoryName(pointDO.getStructCategoryId(), "--"));
        } else {
            pointBO.setStructCategoryName(projectCategoryService.getProjectCategoryId(pointDO.getStructCategoryId()).getCategoryName());
        }
    }

    @Override
    public PageResult<PointBO> getPointRemoveBOPage(PointPageReqVO pageReqVO, Long pointId) {
        //查询工程结构树，只有仪器类型和分组可以点击
        ProjectCategoryDO categoryDO = projectCategoryService.getProjectCategory(pageReqVO.getCategoryId());
        if (Objects.isNull(categoryDO) || (!Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())
                && !Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.GROUP.getType()))) {
            throw exception(ErrorCodeConstants.POINT_SELECT_LEAF_ERROR);
        }
        //要查询的工程分类id集合
        List<Long> categoryIdList = new ArrayList<>();
        categoryIdList.add(pageReqVO.getCategoryId());
        //如果是仪器类型，需要找到下面分组的id
        if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())) {
            List<ProjectCategoryDO> categoryDOList = projectCategoryService.getProjectCategoryListByParentId(pageReqVO.getCategoryId(), false, false);
            if (!CollectionUtils.isEmpty(categoryDOList)) {
                categoryIdList.addAll(categoryDOList.stream().map(ProjectCategoryDO::getId).toList());
            }
        }
        //进行查询
        pageReqVO.setCategoryIdList(categoryIdList);
        PageResult<PointDO> pageResult = getPointPage(pageReqVO);
        //进行转换
        List<PointBO> pointBOList = pageResult.getList().stream().map(item -> {
            PointBO pointBO = new PointBO();
            org.springframework.beans.BeanUtils.copyProperties(item, pointBO);
            formatPointBO(item, pointBO, true);
            return pointBO;
        }).collect(Collectors.toList());
        // 添加以下代码来删除主键id为pointId的元素
        if (!pointBOList.isEmpty()) {
            pointBOList.removeIf(pointBO -> pointBO.getId() != null && pointBO.getId().equals(pointId));
        }
        return new PageResult<>(pointBOList, pageResult.getTotal());
    }

    @Override
    public List<PointDO> listByIdList(List<Long> idList) {
        List<PointDO> pointDOList = pointMapper.selectBatchIds(idList);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(idList)) {
            pointDOList = Optional.ofNullable(pointMapper.selectList(new LambdaQueryWrapperX<PointDO>()
                    .in(PointDO::getId, idList))).orElse(new ArrayList<>());
        }
        return pointDOList;
    }

}