package cn.powerchina.bjy.link.dam.controller.admin.importerrorlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 错误日志新增/修改 Request VO")
@Data
public class ImportErrorLogSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1769")
    private Long id;

    @Schema(description = "测点id", example = "7232")
    private Long pointId;

    @Schema(description = "任务id", example = "28558")
    private Long taskId;

    @Schema(description = "文件名称", example = "张三")
    private String fileName;

    @Schema(description = "任务执行时间")
    private LocalDateTime executeTime;

    @Schema(description = "工作表名称", example = "王五")
    private String sheetName;

    @Schema(description = "错误行")
    private String errorLine;

    @Schema(description = "错误信息", example = "随便")
    private String errorRemark;

}