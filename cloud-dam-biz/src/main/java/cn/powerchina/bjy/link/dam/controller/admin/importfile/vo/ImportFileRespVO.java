package cn.powerchina.bjy.link.dam.controller.admin.importfile.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 导入信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ImportFileRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3696")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id", example = "8231")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "节点id", example = "9170")
    @ExcelProperty("节点id")
    private Long nodeId;

    @Schema(description = "节点id", example = "9170")
    @ExcelProperty("节点名称")
    private String nodeName;

    @Schema(description = "excel文件名称", example = "芋艿")
    @ExcelProperty("excel文件名称")
    private String fileName;

    @Schema(description = "excel文件地址")
    @ExcelProperty("excel文件地址")
    private String filePath;

    @Schema(description = "关联测点数")
    @ExcelProperty("关联测点数")
    private Integer pointNumber;

    @Schema(description = "最新上传时间")
    @ExcelProperty("最新上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "指定导入开始时间")
    @ExcelProperty("指定导入开始时间")
    private LocalDateTime startTime;

    @Schema(description = "指定导入结束时间")
    @ExcelProperty("指定导入结束时间")
    private LocalDateTime endTime;

    @Schema(description = "导入新数据,0否，1是")
    @ExcelProperty("导入新数据,0否，1是")
    private Boolean uploadNew;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
    /**
     * 文件储存路径
     */
    private String fileUrl;

}