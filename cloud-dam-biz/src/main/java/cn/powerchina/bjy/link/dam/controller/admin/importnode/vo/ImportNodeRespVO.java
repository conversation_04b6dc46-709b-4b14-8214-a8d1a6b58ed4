package cn.powerchina.bjy.link.dam.controller.admin.importnode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 节点信息 Response VO")
@Data
//@ExcelIgnoreUnannotated
public class ImportNodeRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21443")
//    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id", example = "15937")
//    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "父节点id", example = "19571")
//    @ExcelProperty("父节点id")
    private Long parentId;

    @Schema(description = "节点名称", example = "李四")
//    @ExcelProperty("节点名称")
    private String name;

    @Schema(description = "节点层级")
//    @ExcelProperty("节点层级")
    private Long hierarchy;

    @Schema(description = "子节点信息")
//    @ExcelProperty("节点层级")
    private List<ImportNodeRespVO> children;

    @Schema(description = "创建时间")
//    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}