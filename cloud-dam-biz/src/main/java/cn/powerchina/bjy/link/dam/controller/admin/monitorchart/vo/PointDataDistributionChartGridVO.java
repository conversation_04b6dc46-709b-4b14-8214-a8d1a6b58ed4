package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description:
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "grid")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartGridVO {

    @Schema(description = "left")
    private String left;

    @Schema(description = "right")
    private String right;

    @Schema(description = "bottom")
    private String bottom;

    @Schema(description = "containLabel")
    private Boolean containLabel;
}
