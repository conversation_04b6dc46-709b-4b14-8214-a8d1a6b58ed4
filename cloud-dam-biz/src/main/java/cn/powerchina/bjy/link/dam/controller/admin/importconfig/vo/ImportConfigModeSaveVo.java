package cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo;

import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigModelDO;
import lombok.Data;

import java.util.List;

@Data
public class ImportConfigModeSaveVo {
    /**
     * 监测数据开始行
     */
    private Integer startLine;

    /**
     * 以自动化监测数据存储,0否，1是
     */
    private Integer isAutomation;

    List<ImportConfigModelVO> modelList;

}
