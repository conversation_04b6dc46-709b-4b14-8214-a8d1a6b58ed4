package cn.powerchina.bjy.link.dam.controller.admin.importtask.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 导入任务 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ImportTaskRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18172")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "任务id", example = "28192")
    @ExcelProperty("任务id")
    private Long taskId;

    @Schema(description = "文件名称", example = "李四")
    @ExcelProperty("文件名称")
    private String fileName;

    @Schema(description = "最新上传时间")
    @ExcelProperty("最新上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "任务执行时间")
    @ExcelProperty("任务执行时间")
    private LocalDateTime executeTime;

    @Schema(description = "已录入测值数")
    @ExcelProperty("已录入测值数")
    private Long uploadNumber;

    @Schema(description = "应录入测值数")
    @ExcelProperty("应录入测值数")
    private Long totalNumber;

    @Schema(description = "执行状态", example = "1")
    @ExcelProperty("执行状态")
    private Integer taskStatus;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "导入人员")
    @ExcelProperty("导入人员")
    private String creator;

}