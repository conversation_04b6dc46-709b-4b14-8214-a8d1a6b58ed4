package cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 导入配置分页 Request VO")
@Data
public class ImportConfigPageReqVO extends PageParam {

    @Schema(description = "导入信息id", example = "16655")
    private Long importId;

    @Schema(description = "工作表名称", example = "芋艿")
    private String sheetName;

    @Schema(description = "测点id", example = "22025")
    private Long pointId;

    @Schema(description = "以自动化监测数据存储,0否，1是")
    private Boolean isAutomation;

    @Schema(description = "分量id", example = "22476")
    private Long instrumentModelId;

    @Schema(description = "分量标识符")
    private String thingIdentity;

    @Schema(description = "分量名称-列")
    private Integer thingColumn;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}