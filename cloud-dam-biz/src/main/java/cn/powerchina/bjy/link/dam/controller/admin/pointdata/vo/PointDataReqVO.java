package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description: 折线图的参数
 * @Author: zhaoqiang
 * @CreateDate: 2024/10/17
 */
@Schema(description = "管理后台 - 测点数据折线图 Request VO")
@Data
@ToString(callSuper = true)
public class PointDataReqVO {

    @Schema(description = "项目id", example = "19338")
    @NotNull(message = "项目不能为空")
    private Long projectId;

    @Schema(description = "测点id", example = "24279")
    @NotNull(message = "测点id不能为空")
    private Long pointId;

    @Schema(description = "监测时间：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] pointTime;

    @Schema(description = "监测时间：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startPointTime;

    @Schema(description = "监测时间：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endPointTime;

    @Schema(description = "采集类型(1：自动化采集，2：人工录入）", example = "1")
    private Integer dataType;

    @Schema(description = "分量id集合")
    @NotNull(message = "分量id不能为空")
    private List<Long> instrumentModelIdList;

    @Schema(description = "数据状态(0：未判定，1：正常数据，2：异常，3：错误数据）", example = "1")
    private List<Integer> dataStatusList;

    @Schema(description = "审核状态（0：未审核；1：审核通过；2：审核不通过）", example = "1")
    private List<Integer> reviewStatusList;

    @Schema(description = "自动化/人工数据比对，默认false")
    private boolean comparison;
}
