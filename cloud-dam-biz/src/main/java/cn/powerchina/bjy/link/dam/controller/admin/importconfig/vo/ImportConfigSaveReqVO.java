package cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 导入配置新增/修改 Request VO")
@Data
public class ImportConfigSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25562")
    private Long id;

    @Schema(description = "导入信息id", example = "16655")
    private Long importId;

    @Schema(description = "工作表名称", example = "芋艿")
    private String sheetName;

    @Schema(description = "测点id", example = "22025")
    private Long pointId;

    @Schema(description = "以自动化监测数据存储,0否，1是")
    private Boolean isAutomation;

    @Schema(description = "分量id", example = "22476")
    private Long instrumentModelId;

    @Schema(description = "分量标识符")
    private String thingIdentity;

    @Schema(description = "分量名称-列", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分量名称-列不能为空")
    private Integer thingColumn;

}