package cn.powerchina.bjy.link.dam.controller.admin.device.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/30
 */
@Data
public class DeviceBO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 父设备号（网关）
     */
    private String parentCode;
    /**
     * 父设备名称
     */
    private String parentName;
    /**
     * 父设备产品编码
     */
    private String parentProductCode;
    /**
     * 父设备产品名称
     */
    private String parentProductName;
    /**
     * 父设备唯一标识
     */
    private String parentSerial;
    /**
     * 设备编码
     */
    private String deviceCode;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * mcu通道编码
     */
    private String mcuChannel;
    /**
     * 设备唯一标识
     */
    private String deviceSerial;
    /**
     * 最后上线时间
     */
    private LocalDateTime lastUpTime;
    /**
     * 测站id
     */
    private Long stationId;
    /**
     * 所属测站
     */
    private String stationName;
    /**
     * 测点id
     */
    private Long pointId;
    /**
     * 测点编号
     */
    private String pointCode;
    /**
     * 测点名称
     */
    private String pointName;
    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 采集方式，1：定点采集，2：间隔采集
     */
    private Integer strategyType;

    /**
     * 时间间隔
     */
    private String timeInterval;

    /**
     * 时间点（英文逗号分隔）
     */
    private String timePoint;
    /**
     * 设备电压
     */
    private String deviceVoltage;
    /**
     * 节点类型(0直连，1网关，2网关子设备）
     */
    private Integer nodeType;

    /**
     * 连接状态（0-离线；1-在线；）
     */
    private Integer linkState;

    /**
     * 是否绑定设备，0：未绑定，1：已绑定
     */
    private Integer bindType;

    /**
     * 设备温度
     */
    private String deviceTemperature;

    /**
     * 设备时钟
     */
    private LocalDateTime deviceClock;

    /**
     * 设备电压上报时间
     */
    private LocalDateTime voltageTime;

    /**
     * 设备温度上报时间
     */
    private LocalDateTime temperatureTime;

    /**
     * 设备时钟上报时间
     */
    private LocalDateTime clockTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastReceiveTime;
}
