package cn.powerchina.bjy.link.dam.service.instrument;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.instrument.vo.InstrumenListReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrument.vo.InstrumentPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrument.vo.InstrumentSaveListReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrument.vo.InstrumentSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.TreeVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 仪器类型 Service 接口
 *
 * <AUTHOR>
 */
public interface InstrumentService {

    /**
     * 创建仪器类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInstrument(@Valid InstrumentSaveReqVO createReqVO);

    /**
     * 更新仪器类型
     *
     * @param updateReqVO 更新信息
     */
    void updateInstrument(@Valid InstrumentSaveReqVO updateReqVO);

    /**
     * 删除仪器类型
     *
     * @param id 编号
     */
    void deleteInstrument(Long id);

    /**
     * 获得仪器类型
     *
     * @param id 编号
     * @return 仪器类型
     */
    InstrumentDO getInstrument(Long id);

    /**
     * 获得仪器类型分页
     *
     * @param pageReqVO 分页查询
     * @return 仪器类型分页
     */
    PageResult<InstrumentDO> getInstrumentPage(InstrumentPageReqVO pageReqVO);

    /**
     * 查看仪器下是否有测点
     *
     * @param id
     * @return
     */
    Boolean checkPoint(Long id);

    /**
     * 获得仪器类型列表
     *
     * @param projectId
     * @return
     */
    List<InstrumentDO> getInstrumentList(Long projectId);

    /**
     * 根据id列表查询
     * @param idList
     * @return
     */
    List<InstrumentDO> listByIdList(List<Long> idList);

    List<TreeVO> getListTree(Long projectId);

    InstrumenListReqVO getInstrumentAndModelAndParam(Long id, Long projectId);

    Long insertListInstrument(InstrumentSaveListReqVO createReqVO);
}