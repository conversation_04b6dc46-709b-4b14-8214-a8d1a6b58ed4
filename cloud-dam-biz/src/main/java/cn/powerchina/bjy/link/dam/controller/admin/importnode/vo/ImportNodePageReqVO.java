package cn.powerchina.bjy.link.dam.controller.admin.importnode.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 节点信息分页 Request VO")
@Data
public class ImportNodePageReqVO extends PageParam {

    @Schema(description = "项目id", example = "15937")
    private Long projectId;

    @Schema(description = "父节点id", example = "19571")
    private Long parentId;

    @Schema(description = "节点名称", example = "李四")
    private String name;

    @Schema(description = "节点层级")
    private Long hierarchy;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}