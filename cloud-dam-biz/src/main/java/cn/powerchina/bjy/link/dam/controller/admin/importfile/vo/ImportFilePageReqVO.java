package cn.powerchina.bjy.link.dam.controller.admin.importfile.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 导入信息分页 Request VO")
@Data
public class ImportFilePageReqVO extends PageParam {

    @Schema(description = "项目id", example = "8231")
    private Long projectId;

    @Schema(description = "节点id", example = "9170")
    private Long nodeId;

    @Schema(description = "excel文件名称", example = "芋艿")
    private String fileName;

    @Schema(description = "excel文件地址")
    private String filePath;

    @Schema(description = "关联测点数")
    private Integer pointNumber;

    @Schema(description = "最新上传时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] uploadTime;

    @Schema(description = "指定导入开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startTime;

    @Schema(description = "指定导入结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] endTime;

    @Schema(description = "导入新数据,0否，1是")
    private Boolean uploadNew;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}