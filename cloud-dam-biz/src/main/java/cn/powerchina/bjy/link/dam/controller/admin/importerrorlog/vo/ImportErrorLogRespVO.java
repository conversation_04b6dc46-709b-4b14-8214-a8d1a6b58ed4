package cn.powerchina.bjy.link.dam.controller.admin.importerrorlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 错误日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ImportErrorLogRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1769")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "测点id", example = "7232")
    @ExcelProperty("测点id")
    private Long pointId;

    @Schema(description = "任务id", example = "28558")
    @ExcelProperty("任务id")
    private Long taskId;

    @Schema(description = "文件名称", example = "张三")
    @ExcelProperty("文件名称")
    private String fileName;

    @Schema(description = "任务执行时间")
    @ExcelProperty("任务执行时间")
    private LocalDateTime executeTime;

    @Schema(description = "工作表名称", example = "王五")
    @ExcelProperty("工作表名称")
    private String sheetName;

    @Schema(description = "错误行")
    @ExcelProperty("错误行")
    private String errorLine;

    @Schema(description = "错误信息", example = "随便")
    @ExcelProperty("错误信息")
    private String errorRemark;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    private String pointCode;

}