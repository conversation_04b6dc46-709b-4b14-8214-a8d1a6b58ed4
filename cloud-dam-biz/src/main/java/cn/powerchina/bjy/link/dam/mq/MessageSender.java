package cn.powerchina.bjy.link.dam.mq;

import cn.powerchina.bjy.link.iot.model.MessageToEdge;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 消息发送
 *
 * <AUTHOR>
 **/
@Component
@Slf4j
public class MessageSender {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    public void sendToRocketMQ(String topic, String tag, MessageToEdge messageToEdge) {
        if (StringUtils.isBlank(topic) || Objects.isNull(messageToEdge)) {
            return;
        }
        try {
            tag = StringUtils.isBlank(tag) ? "*" : tag;
            rocketMQTemplate.convertAndSend((topic + ":" + tag), messageToEdge);
        } catch (Exception e) {
            log.error("send rocketMq message error{}", e.getMessage());
        }
    }
}
