package cn.powerchina.bjy.link.dam.controller.admin.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 大坝设备详情
 */
@Schema(description = "管理后台 - 大坝设备详情 Response VO")
@Data
public class DeviceDetailResVO {

    @Schema(description = "设备基本信息")
    private DeviceBaseInfoVO deviceBaseInfoVO;

    @Schema(description = "设备运行状态")
    private List<DeviceRunInfoVO> deviceRunInfoVOList;
}
