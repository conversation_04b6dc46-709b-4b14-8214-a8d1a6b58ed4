package cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.bo.SelectBO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.enums.InstrumentThingTypeEnum;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 仪器类型-测量分量")
@RestController
@RequestMapping("/dam/instrument/model")
@Validated
public class InstrumentModelController {

    @Resource
    private InstrumentModelService instrumentModelService;

    @PostMapping("/create")
    @Operation(summary = "创建仪器类型-测量分量")
    //@PreAuthorize("@ss.hasPermission('dam:instrument-model:create')")
    public CommonResult<Long> createInstrumentModel(@Valid @RequestBody InstrumentModelSaveReqVO createReqVO) {
        return success(instrumentModelService.createInstrumentModel(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新仪器类型-测量分量")
    //@PreAuthorize("@ss.hasPermission('dam:instrument-model:update')")
    public CommonResult<Boolean> updateInstrumentModel(@Valid @RequestBody InstrumentModelSaveReqVO updateReqVO) {
        instrumentModelService.updateInstrumentModel(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-list")
    @Operation(summary = "批量更新仪器类型-测量分量")
    //@PreAuthorize("@ss.hasPermission('dam:instrument-model:update')")
    public CommonResult<Boolean> updateListInstrumentModel(@Valid @RequestBody List<InstrumentModelSaveReqVO> updateReqVO) {
        instrumentModelService.updateListInstrumentModel(updateReqVO);
        return success(true);
    }

    @PutMapping("/update/formula")
    @Operation(summary = "更新仪器类型-计算公式")
    //@PreAuthorize("@ss.hasPermission('dam:instrument-model:update')")
    public CommonResult<Boolean> updateInstrument(@Valid @RequestBody InstrumentFormulaUpdateReqVO updateReqVO) {
        instrumentModelService.updateInstrumentModel(BeanUtils.toBean(updateReqVO, InstrumentModelSaveReqVO.class));
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除仪器类型-测量分量")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('dam:instrument-model:delete')")
    public CommonResult<Boolean> deleteInstrumentModel(@RequestParam("id") Long id) {
        instrumentModelService.deleteInstrumentModel(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得仪器类型-测量分量")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('dam:instrument-model:query')")
    public CommonResult<InstrumentModelRespVO> getInstrumentModel(@RequestParam("id") Long id) {
        InstrumentModelDO instrumentModel = instrumentModelService.getInstrumentModel(id);
        return success(BeanUtils.toBean(instrumentModel, InstrumentModelRespVO.class));
    }

    @GetMapping("/product/identity")
    @Operation(summary = "获得IOT产品属性标识列表")
    @Parameter(name = "instrumentId", description = "仪器类型id", required = true)
    // @PreAuthorize("@ss.hasPermission('dam:instrument-model:query')")
    public CommonResult<List<SelectRespVO>> getProductIdentity(@RequestParam("instrumentId") Long instrumentId) {
        List<SelectBO> selectBOS = instrumentModelService.selectProductIdentityList(instrumentId);
        return success(BeanUtils.toBean(selectBOS, SelectRespVO.class));
    }
    @GetMapping("/product/getIdentity")
    @Operation(summary = "根据产品Code获得IOT产品属性标识列表")
    @Parameter(name = "productCode", description = "产品Code", required = true)
    public CommonResult<List<SelectRespVO>> getProductCodeIdentity(@RequestParam("productCode") String productCode) {
        List<SelectBO> selectBOS = instrumentModelService.getProductCodeIdentity(productCode);
        return success(BeanUtils.toBean(selectBOS, SelectRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得仪器类型-测量分量分页")
    //@PreAuthorize("@ss.hasPermission('dam:instrument-model:query')")
    public CommonResult<PageResult<InstrumentModelRespVO>> getInstrumentModelPage(@Valid InstrumentModelPageReqVO pageReqVO) {
        PageResult<InstrumentModelRespVO> pageResult = instrumentModelService.getInstrumentModelPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InstrumentModelRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "根据测点id获取测量分量列表")
//    @PreAuthorize("@ss.hasPermission('dam:point:query')")
    @Parameter(name = "pointId", description = "测点id", required = true)
    public CommonResult<List<InstrumentModelRespVO>> getInstrumentModelList(@RequestParam("pointId") Long pointId) {
        List<InstrumentModelDO> instrumentModelList = instrumentModelService.getInstrumentModelList(pointId);
        return success(BeanUtils.toBean(instrumentModelList, InstrumentModelRespVO.class));
    }

    @GetMapping("/instrumentId/list")
    @Operation(summary = "根据仪器类型id获取测量分量列表")
    @Parameter(name = "instrumentId", description = "仪器类型id", required = true)
    public CommonResult<List<InstrumentModelRespVO>> listByInstrumentId(@RequestParam("instrumentId") Long instrumentId) {
        List<InstrumentModelDO> instrumentModelList = instrumentModelService.getModelByInstrumentId(instrumentId);
        return success(BeanUtils.toBean(instrumentModelList, InstrumentModelRespVO.class));
    }

    @GetMapping("/result/list")
    @Operation(summary = "根据测点id和分量类型获取测量分量")
//    @PreAuthorize("@ss.hasPermission('dam:point:query')")
    @Parameter(name = "pointId", description = "测点id", required = true)
    @Parameter(name = "thingTypeList", description = "分量类型，为空默认查询原始和成果分量；1：原始值，2：中间值，3：成果值", required = false)
    public CommonResult<List<InstrumentModelRespVO>> getInstrumentModelResultList(@RequestParam("pointId") Long pointId, @RequestParam(value = "thingTypeList", required = false) List<Integer> thingTypeList) {
        List<InstrumentModelDO> instrumentModelList;
        if (CollectionUtil.isEmpty(thingTypeList)) {
            instrumentModelList = instrumentModelService.getInstrumentModelList(pointId).stream().filter(item -> !InstrumentThingTypeEnum.INTERVAL.getType().equals(item.getThingType())).toList();
        } else {
            instrumentModelList = instrumentModelService.getInstrumentModelList(pointId).stream().filter(item -> thingTypeList.contains(item.getThingType())).toList();
        }
        return success(BeanUtils.toBean(instrumentModelList, InstrumentModelRespVO.class));
    }

}