package cn.powerchina.bjy.link.dam.controller.admin.importfile.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Schema(description = "管理后台 - 分片上传文件 DTO")
@Data
public class FileUploadFragmentationDTO {

    @Schema(description = "excel文件地址")
    private String filePath;

    @Schema(description = "excel文件名称", example = "芋艿")
    private String fileName;

    @Schema(description = "文件附件")
    private MultipartFile file;

    @Schema(description = "文件信息", example = "cloud")
    private byte[] content;

    @Schema(description = "文件批次(第几个分文件)")
    private Integer index;

    @Schema(description = "分片文件总条数")
    private Integer totalChunks;

    @Schema(description = "文件大小")
    private Integer fileSize;

    @Schema(description = "项目id", example = "8231")
    private Long projectId;

}
