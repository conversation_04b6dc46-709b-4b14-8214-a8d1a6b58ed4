package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description: 监测图形-分布图
 * @Author: yangjingt<PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "分布图配置")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartOptionVO {

    @Schema(description = "背景颜色")
    private String backgroundColor;

    @Schema(description = "标题")
    private PointDataDistributionChartTitleVO title;

    @Schema(description = "鼠标提示")
    private PointDataDistributionChartTootipVO tooltip;

    @Schema(description = "图例")
    private PointDataDistributionChartLegendVO legend;

    @Schema(description = "x轴")
    @JsonProperty("xAxis")
    private PointDataDistributionChartXaxisVO xAxis;

    @Schema(description = "y轴")
    @JsonProperty("yAxis")
    private List<PointDataDistributionChartYaxisVO> yAxis;

    @Schema(description = "数据")
    private List<PointDataDistributionChartSeriesVO> series;

    @Schema(description = "dataZoom")
    private List<PointDataDistributionChartDataZoomVO> dataZoom;

    @Schema(description = "toolbox")
    private PointDataDistributionChartToolboxVO toolbox;

    @Schema(description = "grid")
    private PointDataDistributionChartGridVO grid;
}
