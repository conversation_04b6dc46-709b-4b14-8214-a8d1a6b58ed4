package cn.powerchina.bjy.link.dam.dal.mysql.importconfig;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigDO;
import org.apache.ibatis.annotations.Mapper;
import cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo.*;

/**
 * 导入配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ImportConfigMapper extends BaseMapperX<ImportConfigDO> {

    default PageResult<ImportConfigDO> selectPage(ImportConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ImportConfigDO>()
                .eqIfPresent(ImportConfigDO::getImportId, reqVO.getImportId())
                .likeIfPresent(ImportConfigDO::getSheetName, reqVO.getSheetName())
                .eqIfPresent(ImportConfigDO::getPointId, reqVO.getPointId())
                .eqIfPresent(ImportConfigDO::getIsAutomation, reqVO.getIsAutomation())
                .eqIfPresent(ImportConfigDO::getInstrumentModelId, reqVO.getInstrumentModelId())
                .eqIfPresent(ImportConfigDO::getThingIdentity, reqVO.getThingIdentity())
                .eqIfPresent(ImportConfigDO::getThingColumn, reqVO.getThingColumn())
                .betweenIfPresent(ImportConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ImportConfigDO::getId));
    }

}