package cn.powerchina.bjy.link.dam.controller.admin.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备运行状态
 */
@Schema(description = "设备运行状态")
@Data
public class DeviceRunInfoVO {

    @Schema(description = "属性")
    private String item;

    @Schema(description = "上报值")
    private String reportValue;

    @Schema(description = "上报时间")
    private LocalDateTime reportTime;
}
