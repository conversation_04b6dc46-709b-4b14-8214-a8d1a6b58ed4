package cn.powerchina.bjy.link.dam.dal.dataobject.importfile;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;

/**
 * 导入信息 DO
 *
 * <AUTHOR>
 */
@TableName("dam_import_file")
@KeySequence("dam_import_file_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportFileDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 节点id
     */
    private Long nodeId;
    /**
     * excel文件名称
     */
    private String fileName;
    /**
     * excel文件地址
     */
    private String filePath;
    /**
     * 关联测点数
     */
    private Integer pointNumber;
    /**
     * 最新上传时间
     */
    private LocalDateTime uploadTime;
    /**
     * 指定导入开始时间
     */
    private LocalDateTime startTime;
    /**
     * 指定导入结束时间
     */
    private LocalDateTime endTime;
    /**
     * 导入新数据,0否，1是
     */
    private Boolean uploadNew;

    /**
     * 文件储存路径
     */
    private String fileUrl;


}