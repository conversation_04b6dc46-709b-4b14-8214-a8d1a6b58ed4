package cn.powerchina.bjy.link.dam.framework.security.config;

import cn.powerchina.bjy.cloud.framework.security.config.AuthorizeRequestsCustomizer;
import cn.powerchina.bjy.link.dam.enums.ApiConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;

/**
 * Iot 模块的 Security 配置
 */
@Configuration(proxyBeanMethods = false, value = "iotSecurityConfiguration")
public class SecurityConfiguration {

    @Bean("iotAuthorizeRequestsCustomizer")
    public AuthorizeRequestsCustomizer authorizeRequestsCustomizer() {
        return new AuthorizeRequestsCustomizer() {

            @Override
            public void customize(AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
                // TODO：这个每个项目都需要重复配置，得捉摸有没通用的方案
                // Swagger 接口文档
                registry.requestMatchers("/v3/api-docs/**").permitAll() // 元数据
                        .requestMatchers("/swagger-ui.html").permitAll() // Swagger UI
                        .requestMatchers("/swagger-ui/**").permitAll() // Swagger UI 静态资源
                        .requestMatchers("/doc.html").permitAll() // Knife4j UI
                        .requestMatchers("/webjars/**").permitAll() // Swagger UI 依赖的静态资源
                        .requestMatchers("/swagger-resources/**").permitAll() // Swagger 资源
                        .requestMatchers("/favicon.ico").permitAll(); // 网站图标
                // Druid 监控
                registry.requestMatchers("/druid/**").permitAll();
                // Spring Boot Actuator 的安全配置
                registry.requestMatchers("/actuator").permitAll()
                        .requestMatchers("/actuator/**").permitAll();
                // RPC 服务的安全配置
                registry.requestMatchers(ApiConstants.PREFIX + "/**").permitAll();
            }

        };
    }

}
