package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 监测图形-过程线--鼠标提示
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "鼠标提示")
@Data
public class PointDataProcessLineTootipVO {

    @Schema(description = "触发方式")
    private String trigger;

    @Schema(description = "zlevel")
    private Integer zlevel;

    @Schema(description = "zindex")
    private Integer zindex;

}
