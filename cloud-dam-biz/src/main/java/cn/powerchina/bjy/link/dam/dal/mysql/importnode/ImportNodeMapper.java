package cn.powerchina.bjy.link.dam.dal.mysql.importnode;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.link.dam.dal.dataobject.importnode.ImportNodeDO;
import org.apache.ibatis.annotations.Mapper;
import cn.powerchina.bjy.link.dam.controller.admin.importnode.vo.*;

/**
 * 节点信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ImportNodeMapper extends BaseMapperX<ImportNodeDO> {

    default PageResult<ImportNodeDO> selectPage(ImportNodePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ImportNodeDO>()
                .eqIfPresent(ImportNodeDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ImportNodeDO::getParentId, reqVO.getParentId())
                .likeIfPresent(ImportNodeDO::getName, reqVO.getName())
                .eqIfPresent(ImportNodeDO::getHierarchy, reqVO.getHierarchy())
                .betweenIfPresent(ImportNodeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ImportNodeDO::getId));
    }

}