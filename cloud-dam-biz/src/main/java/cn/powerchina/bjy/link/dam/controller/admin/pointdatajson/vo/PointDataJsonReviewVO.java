package cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点数据json Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointDataJsonReviewVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "406")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "采集类型(1：自动化采集，2：人工录入）", example = "1")
    private Integer dataType;

    private LocalDateTime pointTime;

    @Schema(description = "审核状态（0：未审核；1：审核通过；2：审核不通过）")
    private Integer reviewStatus;

    @Schema(description = "审核人id")
    private String reviewer;

    @Schema(description = "审核人名称")
    @Length(max = 32)
    private String reviewName;

    @Schema(description = "审核人意见")
    @Length(max = 256)
    private String reviewOpinion;

    @Schema(description = "项目id", example = "19338")
    private Long projectId;

    @Schema(description = "测点id", example = "24279")
    private Long pointId;

    private Long instrumentId;


}