package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 监测图形-过程线的响应
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-过程线响应")
@Data
public class PointDataProcessLineResVO {

    @Schema(description = "唯一标识")
    private Long id;

    private String instance;

    @Schema(description = "配置")
    private PointDataProcessLineOptionVO option;
}