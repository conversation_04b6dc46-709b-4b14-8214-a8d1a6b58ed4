<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.link.dam.dal.mysql.projectuser.ProjectUserMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <select id="selectProjectNameByUserId" resultType="java.lang.String" parameterType="java.lang.Long">
        select project.project_name
        from dam_project project
                 left join dam_project_user rel
                           on rel.project_id = project.id
        where rel.user_id = #{userId}
          and rel.deleted = 0
          and project.deleted = 0
    </select>

    <select id="selectUserIdByProjectId" resultType="java.lang.Long" parameterType="java.lang.Long">
        select user_id
        from dam_project_user
        where project_id = #{projectId}
          and deleted = 0
    </select>

</mapper>