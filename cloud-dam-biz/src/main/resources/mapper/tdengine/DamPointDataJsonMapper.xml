<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.link.dam.dal.tdengine.DamPointDataJsonMapper">

    <select id="listMaxPointTimeByPointIdListTdengine" resultType="Map">
        select last(ts) as pointTime, point_id as pointId from  point_data_${projectId}_${instrumentId}
        where point_id in
        <foreach item="item" index="index" collection="pointIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by pointId
    </select>

    <select id="list4ProcessLineTdengine" resultType="Map">
        select * from point_data_${projectId}_${instrumentId}
        <where>
        <if test="pointTimeList != null and pointTimeList.size() > 0">
            <foreach collection="pointTimeList" item="item" open="and (" separator="or" close=")">
                (point_id = #{item.pointId} and ts between #{item.minTime} and #{item.maxTime})
            </foreach>
        </if>
        <if test="dataTypeList != null and dataTypeList.size() > 0">
            <if test="dataTypeList.size()!=2">  <!--长度为2代表选择了所有采集类型-->
                <foreach collection="dataTypeList" item="item" open="and data_type in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="dataStatusList != null and dataStatusList.size() > 0">
            <if test="dataStatusList.size()!=4">  <!--长度为4代表选择了所有数据状态-->
                <foreach collection="dataStatusList" item="item" open="and data_status in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="reviewStatusList != null and reviewStatusList.size() > 0">
            <if test="reviewStatusList.size()!=3">  <!--长度为3代表选择了所有审核状态-->
                <foreach collection="reviewStatusList" item="item" open="and review_status in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        </where>
    </select>

    <select id="list4DistributionChartTdengine" resultType="Map">
        select * from point_data_${projectId}_${instrumentId}
        <where>
        <if test="pointIdList != null and pointIdList.size() > 0">
            <foreach collection="pointIdList" item="item" open="and point_id in (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pointTimeList != null and pointTimeList.size() > 0">
            <foreach collection="pointTimeList" item="item" open="and (" separator="or" close=")">
                ts between #{item.minTime} and #{item.maxTime}
            </foreach>
        </if>
        <if test="dataTypeList != null and dataTypeList.size() > 0">
            <if test="dataTypeList.size()!=2"> <!--长度为2代表选择了所有采集类型-->
                <foreach collection="dataTypeList" item="item" open="and data_type in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="dataStatusList != null and dataStatusList.size() > 0">
            <if test="dataStatusList.size()!=4"> <!--长度为4代表选择了所有数据状态-->
                <foreach collection="dataStatusList" item="item" open="and data_status in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="reviewStatusList != null and reviewStatusList.size() > 0">
            <if test="reviewStatusList.size()!=3"> <!--长度为3代表选择了所有审核状态-->
                <foreach collection="reviewStatusList" item="item" open="and review_status in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        </where>
    </select>

</mapper>