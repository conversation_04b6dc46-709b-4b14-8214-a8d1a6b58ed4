<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.link.dam.dal.mysql.pointevaluate.PointEvaluateMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->
    <select id="selectList1"  parameterType="cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluateReqVO"
            resultType="cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluatePageRespVO">
        select eva.id as                id,
        model.project_id         projectId,
        p.point_code             pointCode,
        p.point_state             pointState,
        model.thing_name         thingName,
        model.thing_unit         thingUnit,
        model.id                 instrumentModelId,
        eva.apply_type           applyType,
        eva.waring_down          waringDown,
        eva.waring_up            waringUp,
        eva.abnormal_up          abnormalUp,
        eva.abnormal_down        abnormalDown,
        eva.effective_start_time effectiveStartTime,
        eva.effective_end_time   effectiveEndTime,
        eva.evaluate_extreme_id   evaluateExtremeId,
        eva.point_id         pointId
        from dam_instrument_model model
        left join dam_point_evaluate eva on model.id = eva.instrument_model_id
        <if test="pointIdList != null and pointIdList.size() > 0">
           and eva.point_id in
            <foreach collection="pointIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        left join dam_point p on p.id = eva.point_id
        where model.deleted = 0 and eva.deleted=0 and model.project_id= #{projectId}
        <if test="applyType != null">
            and eva.apply_type= #{applyType}
        </if>
        <if test="pointState != null">
            and p.point_state= #{pointState}
        </if>
        order by p.point_code ,model.id,model.thing_name,eva.id;
    </select>

    <select id="selectModelList"  parameterType="cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluateReqVO"
            resultType="cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluatePageRespVO">
        select model.project_id projectId,
        model.thing_name thingName,
        model.thing_unit thingUnit,
        model.id         instrumentModelId,
        model.up_limit   abnormalUp,
        model.down_limit abnormalDown,
        p.id             pointId,
        p.point_state             pointState,
        p.point_code             pointCode
        from dam_instrument_model model
        left join dam_point p on p.instrument_id = model.instrument_id
        where model.deleted = 0
        and model.project_id= #{projectId}
        <if test="pointIdList != null and pointIdList.size() > 0">
            and p.id in
            <foreach collection="pointIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by p.point_code ,model.id,model.thing_name;
    </select>
    <select id="selectSelectedList"
            resultType="cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluatePageRespVO">
            select
                p.point_code,
                eva.apply_type,
                eva.waring_down,
                eva.waring_up,
                eva.abnormal_up,
                eva.abnormal_down,
                eva.effective_start_time,
                eva.effective_end_time,
                eva.point_id,
                eva.project_id
            from   dam_point_evaluate eva
            left join dam_point p on p.id = eva.point_id
            where eva.evaluate_extreme_id = #{extremeId}
    </select>

</mapper>