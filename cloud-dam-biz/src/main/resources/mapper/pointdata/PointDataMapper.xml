<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.link.dam.dal.mysql.pointdata.PointDataMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->
    <update id="batchUpdateDelete">
        update dam_point_data set deleted= 1,rollback_import_id = #{rollbackImportId}  WHERE point_id = #{pointId}
        AND point_time BETWEEN #{startTime} AND #{endTime}
        AND data_type = #{dataType} and deleted=0
    </update>

    <update id="batchUpdateRecover">
        update dam_point_data set deleted= 0  WHERE point_id = #{pointId}
        AND point_time BETWEEN #{startTime} AND #{endTime}
        AND data_type = #{dataType} and deleted=1 and rollback_import_id = #{rollbackImportId}
    </update>

    <select id="yearAndMonthStatistic" resultType="cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo.PointDataBO">
        SELECT
        DATE_FORMAT(point_time, '%Y-%m') as yearAndMonth,point_id pointId,
        <if test="!useAbsoluteValue">
        min(thing_value) valueMin,max(thing_value) valueMax,truncate(avg(thing_value),2) valueAverage,
        </if>
        <if test="useAbsoluteValue">
            min(absolute_value) valueMin,max(absolute_value) valueMax,truncate(avg(thing_value),2) valueAverage,
        </if>
        count(*) as countNum
        FROM dam_point_data
        WHERE deleted = 0
        AND point_id = #{pointId}
        AND instrument_model_id = #{instrumentModelId}
        AND point_time BETWEEN #{startTime} AND #{endTime}
        <if test="dataTypeList != null and dataTypeList.size() > 0">
            and data_type in
            <foreach collection="dataTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dataStatusList != null and dataStatusList.size() > 0">
            and data_status in
            <foreach collection="dataStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY DATE_FORMAT(point_time, '%Y-%m')
        ORDER BY DATE_FORMAT(point_time, '%Y-%m');
    </select>

    <select id="yearStatistic" resultType="cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo.PointDataBO">
        SELECT
        year(point_time) as yearAndMonth,point_id pointId,
        <if test="!useAbsoluteValue">
            min(thing_value) valueMin,max(thing_value) valueMax,truncate(avg(thing_value),2) valueAverage,
        </if>
        <if test="useAbsoluteValue">
            min(absolute_value) valueMin,max(absolute_value) valueMax,truncate(avg(thing_value),2) valueAverage,
        </if>
        count(*) as countNum
        FROM dam_point_data
        WHERE deleted = 0
        AND point_id = #{pointId}
        AND instrument_model_id = #{instrumentModelId}
        AND point_time BETWEEN #{startTime} AND #{endTime}
        <if test="dataTypeList != null and dataTypeList.size() > 0">
            and data_type in
            <foreach collection="dataTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dataStatusList != null and dataStatusList.size() > 0">
            and data_status in
            <foreach collection="dataStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY year(point_time)
        ORDER BY year(point_time);
    </select>

    <select id="selectYearAndMonthValuePointTime">
        select d1.yearMonth, d1.pointId, d2.point_time valuePointTime
        from (SELECT DATE_FORMAT(point_time, '%Y-%m') AS yearMonth,point_id as pointId,
        <if test="isMax == 1 and !useAbsoluteValue">
            MAX(thing_value)                 AS result_value
        </if>
        <if test="isMax != 1 and !useAbsoluteValue">
            MIN(thing_value)                 AS result_value
        </if>
        <if test="isMax == 1 and useAbsoluteValue">
            MAX(absolute_value)                 AS result_value
        </if>
        <if test="isMax != 1 and useAbsoluteValue">
            MIN(absolute_value)                 AS result_value
        </if>
        FROM dam_point_data
        WHERE deleted = 0
        AND point_id = #{pointId}
        AND instrument_model_id = #{instrumentModelId}
        AND point_time BETWEEN #{startTime} AND #{endTime}
        <if test="dataTypeList != null and dataTypeList.size() > 0">
            and data_type in
            <foreach collection="dataTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dataStatusList != null and dataStatusList.size() > 0">
            and data_status in
            <foreach collection="dataStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY DATE_FORMAT(point_time, '%Y-%m')) d1
        left join dam_point_data d2
        on d1.result_value = d2.thing_value and d1.yearMonth = DATE_FORMAT(d2.point_time, '%Y-%m')
        where deleted = 0
        AND point_id = #{pointId}
        AND instrument_model_id = #{instrumentModelId}
        AND point_time BETWEEN #{startTime} AND #{endTime}
        <if test="dataTypeList != null and dataTypeList.size() > 0">
            and data_type in
            <foreach collection="dataTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dataStatusList != null and dataStatusList.size() > 0">
            and data_status in
            <foreach collection="dataStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by d2.point_time;
    </select>

    <select id="selectYearValuePointTime">
        select d1.yearMonth,  d1.pointId, d2.point_time valuePointTime
        from (SELECT year(point_time) AS yearMonth,point_id as pointId,
        <if test="isMax == 1 and !useAbsoluteValue">
            MAX(thing_value)                 AS result_value
        </if>
        <if test="isMax != 1 and !useAbsoluteValue">
            MIN(thing_value)                 AS result_value
        </if>
        <if test="isMax == 1 and useAbsoluteValue">
            MAX(absolute_value)                 AS result_value
        </if>
        <if test="isMax != 1 and useAbsoluteValue">
            MIN(absolute_value)                 AS result_value
        </if>
        FROM dam_point_data
        WHERE deleted = 0
        AND point_id = #{pointId}
        AND instrument_model_id = #{instrumentModelId}
        AND point_time BETWEEN #{startTime} AND #{endTime}
        <if test="dataTypeList != null and dataTypeList.size() > 0">
            and data_type in
            <foreach collection="dataTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dataStatusList != null and dataStatusList.size() > 0">
            and data_status in
            <foreach collection="dataStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY year(point_time)) d1
        left join dam_point_data d2
        on d1.result_value = d2.thing_value and d1.yearMonth = DATE_FORMAT(d2.point_time, '%Y-%m')
        where deleted = 0
        AND point_id = #{pointId}
        AND instrument_model_id = #{instrumentModelId}
        AND point_time BETWEEN #{startTime} AND #{endTime}
        <if test="dataTypeList != null and dataTypeList.size() > 0">
            and data_type in
            <foreach collection="dataTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dataStatusList != null and dataStatusList.size() > 0">
            and data_status in
            <foreach collection="dataStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by d2.point_time;
    </select>
</mapper>