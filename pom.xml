<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.powerchina.bjy</groupId>
    <artifactId>cloud-link-dam</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <modules>
        <module>cloud-dam-api</module>
        <module>cloud-dam-biz</module>
    </modules>

    <name>${project.artifactId}</name>
    <description>
        项目基础脚手架
        本地默认端口：

    </description>

    <properties>
        <!--当前系统版本-->
        <revision>1.0.0</revision>
        <!--其他版本-->
        <revision.dependencies>1.0.0</revision.dependencies>
        <!--system版本-->
        <revision.system>1.0.13</revision.system>
        <!--infra系统版本-->
        <revision.infra>1.0.3</revision.infra>
        <!--iot系统版本-->
        <revision.iot>1.0.1-SNAPSHOT</revision.iot>
        <!--framework版本-->
        <revision.framework>1.0.4</revision.framework>
        <!-- Maven 相关 -->
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
        <!-- 看看咋放到 bom 里 -->
        <lombok.version>1.18.30</lombok.version>
        <spring.boot.version>3.2.0</spring.boot.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.powerchina.bjy</groupId>
                <artifactId>cloud-dependencies</artifactId>
                <version>${revision.dependencies}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>cn.powerchina.bjy</groupId>
                <artifactId>cloud-spring-boot-starter-web</artifactId>
                <version>${revision.framework}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- maven-surefire-plugin 插件，用于运行单元测试。 -->
                <!-- 注意，需要使用 3.0.X+，因为要支持 Junit 5 版本 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
                <!-- maven-compiler-plugin 插件，解决 Lombok + MapStruct 组合 -->
                <!-- https://stackoverflow.com/questions/33483697/re-run-spring-boot-configuration-annotation-processor-to-update-generated-metada -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                                <version>${spring.boot.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                        <!-- 编译参数写在 arg 内，解决 Spring Boot 3.2 的 Parameter Name Discovery 问题 -->
                        <debug>false</debug>
                        <compilerArgs>
                            <arg>-parameters</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://10.216.3.8:8083/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://10.216.3.8:8083/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <!-- 使用 huawei / aliyun 的 Maven 源，提升下载速度 -->
    <repositories>
        <repository>
            <id>huaweicloud</id>
            <name>huawei</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </repository>
        <repository>
            <id>aliyunmaven</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
    </repositories>

</project>
